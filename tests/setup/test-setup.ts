import { beforeEach, afterEach, vi } from 'vitest';
import { DatabaseConnection } from '../../src/database/connection/database-connection';
import { DatabaseSchema } from '../../src/database/migrations/schema';

// Configuração global para todos os testes
let testDb: DatabaseConnection;
let originalGetInstance: any;

beforeEach(() => {
  // Criar banco em memória para cada teste
  testDb = new DatabaseConnection(':memory:');

  // Inicializar schema
  const schema = new DatabaseSchema(testDb.getDatabase());
  schema.initializeDatabase();

  // Mock do DatabaseConnection.getInstance para usar banco de teste
  originalGetInstance = DatabaseConnection.getInstance;
  DatabaseConnection.getInstance = vi.fn().mockReturnValue(testDb);

  // Disponibilizar globalmente para os testes
  (global as any).testDb = testDb;
});

afterEach(() => {
  // Restaurar método original
  if (originalGetInstance) {
    DatabaseConnection.getInstance = originalGetInstance;
  }

  // Limpar banco após cada teste
  if (testDb) {
    testDb.close();
  }

  // Limpar todos os mocks
  vi.clearAllMocks();
});
