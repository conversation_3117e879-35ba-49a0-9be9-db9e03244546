import { describe, it, expect, beforeEach } from 'vitest';
import { PaymentService } from '../../../src/services/payment-service';
import { OutboxService } from '../../../src/services/outbox-service';
import paymentsFixture from '../../fixtures/payments.json';

describe('PaymentService - Testes Básicos', () => {
  let paymentService: PaymentService;

  beforeEach(() => {
    paymentService = new PaymentService();
  });

  describe('createPaymentWithOutbox', () => {
    it('deve criar pagamento e evento outbox em transação atômica', () => {
      // Arrange
      const paymentData = paymentsFixture.validPayment;

      // Act
      const result = paymentService.createPaymentWithOutbox(paymentData);

      // Assert
      expect(result).toBeDefined();
      expect(result.pagamento).toBeDefined();
      expect(result.outboxEvent).toBeDefined();

      // Verificar pagamento
      const payment = result.pagamento;
      expect(payment.id).toBeGreaterThan(0);
      expect(payment.valor).toBe(paymentData.valor);
      expect(payment.descricao).toBe(paymentData.descricao);
      expect(payment.status).toBe('pendente');
      expect(payment.created_at).toBeDefined();
      expect(payment.updated_at).toBeDefined();

      // Verificar evento outbox
      const event = result.outboxEvent;
      expect(event.event_type).toBe('payment_created');
      expect(event.aggregate_id).toBe(payment.id);
      expect(event.external_id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      expect(event.status).toBe('pending');
      expect(event.retry_count).toBe(0);

      // Verificar payload do evento (contém o pagamento + external_id)
      const payload = JSON.parse(event.payload);
      expect(payload.id).toBe(payment.id);
      expect(payload.valor).toBe(payment.valor);
      expect(payload['id-externo']).toBe(event.external_id);
    });

    it('deve gerar UUID único para cada pagamento', () => {
      // Arrange
      const paymentData = paymentsFixture.validPayment;

      // Act
      const result1 = paymentService.createPaymentWithOutbox(paymentData);
      const result2 = paymentService.createPaymentWithOutbox(paymentData);

      // Assert
      expect(result1.pagamento.id).not.toBe(result2.pagamento.id);
      expect(result1.outboxEvent.external_id).not.toBe(result2.outboxEvent.external_id);
      expect(result1.outboxEvent.external_id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      expect(result2.outboxEvent.external_id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
    });

    it('deve criar evento outbox com payload correto', () => {
      // Arrange
      const paymentData = paymentsFixture.highValuePayment;

      // Act
      const result = paymentService.createPaymentWithOutbox(paymentData);

      // Assert
      const payment = result.pagamento;
      const event = result.outboxEvent;
      const payload = JSON.parse(event.payload);

      expect(payload).toEqual({
        id: payment.id,
        valor: payment.valor,
        descricao: payment.descricao,
        status: payment.status,
        created_at: payment.created_at,
        updated_at: payment.updated_at,
        'id-externo': event.external_id
      });
    });

    it('deve rejeitar pagamento com valor inválido', () => {
      // Arrange
      const invalidPaymentData = {
        valor: -10.00,
        descricao: "Valor negativo inválido"
      };

      // Act & Assert
      expect(() => paymentService.createPaymentWithOutbox(invalidPaymentData))
        .toThrow();

      // Verificar que nenhum evento outbox foi criado
      const outboxService = new OutboxService();
      const events = outboxService.getPendingEvents(10);

      // Não deve haver eventos para pagamentos inválidos
      const invalidEvents = events.filter(e => {
        const payload = JSON.parse(e.payload);
        return payload.valor === -10.00;
      });
      expect(invalidEvents).toHaveLength(0);
    });

    it('deve criar pagamento com micro transação', () => {
      // Arrange
      const microPaymentData = paymentsFixture.microPayment;

      // Act
      const result = paymentService.createPaymentWithOutbox(microPaymentData);

      // Assert
      expect(result).toBeDefined();
      expect(result.pagamento.valor).toBe(0.10);
      expect(result.pagamento.descricao).toBe(microPaymentData.descricao);

      // Verificar evento outbox
      expect(result.outboxEvent).toBeDefined();
      expect(result.outboxEvent.event_type).toBe('payment_created');
      expect(result.outboxEvent.external_id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      expect(result.outboxEvent.aggregate_id).toBe(result.pagamento.id);
    });
  });
});
