import { describe, it, expect, beforeEach } from 'vitest';
import { OutboxService } from '../../../src/services/outbox-service';
import { OutboxRepository } from '../../../src/database/repositories/outbox-repository';

describe('OutboxService', () => {
  let outboxService: OutboxService;
  let outboxRepository: OutboxRepository;

  beforeEach(() => {
    outboxRepository = new OutboxRepository();
    outboxService = new OutboxService();
  });

  describe('markAsFailed', () => {
    it('deve marcar como failed quando retry_count < 3', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'pending',
        retry_count: 1
      });

      const errorMessage = 'API returned status 429: Too Many Requests';

      // Act
      const updatedEvent = outboxService.markAsFailed(event.id, 2, errorMessage);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('failed');
      expect(updatedEvent!.retry_count).toBe(2);
      expect(updatedEvent!.error_message).toBe(errorMessage);
      expect(updatedEvent!.last_retry_at).toBeDefined();
    });

    it('deve marcar como error quando retry_count >= 3', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        retry_count: 2
      });

      const errorMessage = 'Maximum retry attempts reached';

      // Act
      const updatedEvent = outboxService.markAsFailed(event.id, 3, errorMessage);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('error');
      expect(updatedEvent!.retry_count).toBe(3);
      expect(updatedEvent!.error_message).toBe(errorMessage);
      expect(updatedEvent!.last_retry_at).toBeDefined();
    });

    it('deve atualizar last_retry_at com timestamp atual', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'pending',
        retry_count: 0
      });

      const beforeTime = new Date();

      // Act
      const updatedEvent = outboxService.markAsFailed(event.id, 1, 'Test error');

      // Assert
      expect(updatedEvent!.last_retry_at).toBeDefined();
      const lastRetryTime = new Date(updatedEvent!.last_retry_at!);
      expect(lastRetryTime.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
    });
  });

  describe('resetFailedEvent', () => {
    it('deve resetar evento failed para pending', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        retry_count: 2,
        error_message: 'Previous error',
        last_retry_at: new Date().toISOString()
      });

      // Act
      const resetEvent = outboxService.resetFailedEvent(event.id);

      // Assert
      expect(resetEvent).toBeDefined();
      expect(resetEvent!.status).toBe('pending');
      expect(resetEvent!.error_message).toBeUndefined();
      expect(resetEvent!.last_retry_at).toBeUndefined();
      expect(resetEvent!.retry_count).toBe(2); // retry_count mantido
    });

    it('deve retornar undefined para evento inexistente', () => {
      // Act
      const result = outboxService.resetFailedEvent(999);

      // Assert
      expect(result).toBeUndefined();
    });
  });

  describe('createPaymentEvent', () => {
    it('deve criar evento outbox para pagamento', () => {
      // Arrange
      const payment = {
        id: 1,
        valor: 100.50,
        descricao: 'Test payment',
        status: 'pendente',
        'id-externo': 'test-uuid',
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z'
      };

      // Act
      const event = outboxService.createPaymentEvent(payment);

      // Assert
      expect(event).toBeDefined();
      expect(event.event_type).toBe('payment_created');
      expect(event.aggregate_id).toBe(payment.id);
      expect(event.external_id).toBe(payment['id-externo']);
      expect(event.status).toBe('pending');
      expect(event.retry_count).toBe(0);

      const payload = JSON.parse(event.payload);
      expect(payload).toEqual(payment);
    });
  });

  describe('getStatistics', () => {
    it('deve retornar estatísticas corretas', () => {
      // Arrange
      // Criar eventos com diferentes status
      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'pending-1',
        payload: '{}',
        status: 'pending'
      });

      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 2,
        external_id: 'processed-1',
        payload: '{}',
        status: 'processed'
      });

      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 3,
        external_id: 'failed-1',
        payload: '{}',
        status: 'failed'
      });

      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 4,
        external_id: 'error-1',
        payload: '{}',
        status: 'error'
      });

      // Act
      const stats = outboxService.getStatistics();

      // Assert
      expect(stats).toEqual({
        pending: 1,
        processed: 1,
        failed: 1,
        error: 1
      });
    });

    it('deve retornar zeros quando não há eventos', () => {
      // Act
      const stats = outboxService.getStatistics();

      // Assert
      expect(stats).toEqual({
        pending: 0,
        processed: 0,
        failed: 0,
        error: 0
      });
    });
  });

  describe('getPendingEvents', () => {
    it('deve retornar apenas eventos pending', () => {
      // Arrange
      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'pending-1',
        payload: '{}',
        status: 'pending'
      });

      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 2,
        external_id: 'processed-1',
        payload: '{}',
        status: 'processed'
      });

      // Act
      const pendingEvents = outboxService.getPendingEvents(10);

      // Assert
      expect(pendingEvents).toHaveLength(1);
      expect(pendingEvents[0].status).toBe('pending');
      expect(pendingEvents[0].external_id).toBe('pending-1');
    });

    it('deve respeitar limite de eventos', () => {
      // Arrange
      for (let i = 1; i <= 5; i++) {
        outboxRepository.create({
          event_type: 'payment_created',
          aggregate_id: i,
          external_id: `pending-${i}`,
          payload: '{}',
          status: 'pending'
        });
      }

      // Act
      const pendingEvents = outboxService.getPendingEvents(3);

      // Assert
      expect(pendingEvents).toHaveLength(3);
    });
  });
});
