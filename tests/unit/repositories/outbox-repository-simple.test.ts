import { describe, it, expect, beforeEach } from 'vitest';
import { OutboxRepository } from '../../../src/database/repositories/outbox-repository';

// Helper para gerar external_id únicos
let testCounter = 0;
const generateUniqueExternalId = () => `test-uuid-${++testCounter}-${Date.now()}`;

describe('OutboxRepository - Testes Básicos', () => {
  let outboxRepository: OutboxRepository;

  beforeEach(() => {
    outboxRepository = new OutboxRepository();
  });

  describe('create', () => {
    it('deve criar evento outbox com dados válidos', () => {
      // Arrange
      const eventData = {
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: JSON.stringify({ test: 'data' }),
        status: 'pending' as const,
        retry_count: 0
      };

      // Act
      const event = outboxRepository.create(eventData);

      // Assert
      expect(event).toBeDefined();
      expect(event.id).toBeGreaterThan(0);
      expect(event.event_type).toBe(eventData.event_type);
      expect(event.aggregate_id).toBe(eventData.aggregate_id);
      expect(event.external_id).toBe(eventData.external_id);
      expect(event.payload).toBe(eventData.payload);
      expect(event.status).toBe(eventData.status);
      expect(event.retry_count).toBe(eventData.retry_count);
      expect(event.created_at).toBeDefined();
    });

    it('deve usar valores padrão quando não especificados', () => {
      // Arrange
      const eventData = {
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: '{}'
      };

      // Act
      const event = outboxRepository.create(eventData);

      // Assert
      expect(event.status).toBe('pending');
      expect(event.retry_count).toBe(0);
    });
  });

  describe('findById', () => {
    it('deve encontrar evento por ID', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: '{}'
      });

      // Act
      const foundEvent = outboxRepository.findById(event.id);

      // Assert
      expect(foundEvent).toBeDefined();
      expect(foundEvent!.id).toBe(event.id);
      expect(foundEvent!.external_id).toBe(event.external_id);
    });

    it('deve retornar undefined para ID inexistente', () => {
      // Act
      const foundEvent = outboxRepository.findById(999);

      // Assert
      expect(foundEvent).toBeUndefined();
    });
  });

  describe('markAsFailed', () => {
    it('deve marcar evento como failed com retry_count < 3', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: '{}'
      });

      const errorMessage = 'Test error message';

      // Act
      const updatedEvent = outboxRepository.markAsFailed(event.id, 1, errorMessage);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('failed');
      expect(updatedEvent!.retry_count).toBe(1);
      expect(updatedEvent!.error_message).toBe(errorMessage);
      expect(updatedEvent!.last_retry_at).toBeDefined();
    });

    it('deve marcar evento como error com retry_count >= 3', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: '{}'
      });

      const errorMessage = 'Maximum retries exceeded';

      // Act
      const updatedEvent = outboxRepository.markAsFailed(event.id, 3, errorMessage);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('error');
      expect(updatedEvent!.retry_count).toBe(3);
      expect(updatedEvent!.error_message).toBe(errorMessage);
    });
  });

  describe('markAsProcessed', () => {
    it('deve marcar evento como processed', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: '{}'
      });

      // Act
      const updatedEvent = outboxRepository.markAsProcessed(event.id);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('processed');
      expect(updatedEvent!.processed_at).toBeDefined();
    });
  });
});
