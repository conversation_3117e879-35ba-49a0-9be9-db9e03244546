import { describe, it, expect, beforeEach } from 'vitest';
import { OutboxRepository } from '../../../src/database/repositories/outbox-repository';

// Helper para gerar external_id únicos
let testCounter = 0;
const generateUniqueExternalId = () => `test-uuid-${++testCounter}-${Date.now()}`;

describe('OutboxRepository', () => {
  let outboxRepository: OutboxRepository;

  beforeEach(() => {
    outboxRepository = new OutboxRepository();
  });

  describe('create', () => {
    it('deve criar evento outbox com dados válidos', () => {
      // Arrange
      const eventData = {
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: JSON.stringify({ test: 'data' }),
        status: 'pending' as const,
        retry_count: 0
      };

      // Act
      const event = outboxRepository.create(eventData);

      // Assert
      expect(event).toBeDefined();
      expect(event.id).toBeGreaterThan(0);
      expect(event.event_type).toBe(eventData.event_type);
      expect(event.aggregate_id).toBe(eventData.aggregate_id);
      expect(event.external_id).toBe(eventData.external_id);
      expect(event.payload).toBe(eventData.payload);
      expect(event.status).toBe(eventData.status);
      expect(event.retry_count).toBe(eventData.retry_count);
      expect(event.created_at).toBeDefined();
    });

    it('deve usar valores padrão quando não especificados', () => {
      // Arrange
      const eventData = {
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: '{}'
      };

      // Act
      const event = outboxRepository.create(eventData);

      // Assert
      expect(event.status).toBe('pending');
      expect(event.retry_count).toBe(0);
    });

    it('deve gerar ID único para cada evento', () => {
      // Arrange
      const eventData = {
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: generateUniqueExternalId(),
        payload: '{}'
      };

      // Act
      const event1 = outboxRepository.create(eventData);
      const event2 = outboxRepository.create({
        ...eventData,
        external_id: generateUniqueExternalId()
      });

      // Assert
      expect(event1.id).not.toBe(event2.id);
      expect(event1.id).toBeGreaterThan(0);
      expect(event2.id).toBeGreaterThan(0);
    });
  });

  describe('findById', () => {
    it('deve encontrar evento por ID', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}'
      });

      // Act
      const foundEvent = outboxRepository.findById(event.id);

      // Assert
      expect(foundEvent).toBeDefined();
      expect(foundEvent!.id).toBe(event.id);
      expect(foundEvent!.external_id).toBe(event.external_id);
    });

    it('deve retornar undefined para ID inexistente', () => {
      // Act
      const foundEvent = outboxRepository.findById(999);

      // Assert
      expect(foundEvent).toBeUndefined();
    });
  });

  describe('findByExternalId', () => {
    it('deve encontrar evento por external_id', () => {
      // Arrange
      const externalId = 'unique-external-id';
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: externalId,
        payload: '{}'
      });

      // Act
      const foundEvent = outboxRepository.findByExternalId(externalId);

      // Assert
      expect(foundEvent).toBeDefined();
      expect(foundEvent!.id).toBe(event.id);
      expect(foundEvent!.external_id).toBe(externalId);
    });

    it('deve retornar undefined para external_id inexistente', () => {
      // Act
      const foundEvent = outboxRepository.findByExternalId('non-existent');

      // Assert
      expect(foundEvent).toBeUndefined();
    });
  });

  describe('markAsFailed', () => {
    it('deve marcar evento como failed com retry_count < 3', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}'
      });

      const errorMessage = 'Test error message';

      // Act
      const updatedEvent = outboxRepository.markAsFailed(event.id, 1, errorMessage);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('failed');
      expect(updatedEvent!.retry_count).toBe(1);
      expect(updatedEvent!.error_message).toBe(errorMessage);
      expect(updatedEvent!.last_retry_at).toBeDefined();
    });

    it('deve marcar evento como error com retry_count >= 3', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}'
      });

      const errorMessage = 'Maximum retries exceeded';

      // Act
      const updatedEvent = outboxRepository.markAsFailed(event.id, 3, errorMessage);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('error');
      expect(updatedEvent!.retry_count).toBe(3);
      expect(updatedEvent!.error_message).toBe(errorMessage);
    });

    it('deve retornar undefined para ID inexistente', () => {
      // Act
      const result = outboxRepository.markAsFailed(999, 1, 'error');

      // Assert
      expect(result).toBeUndefined();
    });
  });

  describe('markAsProcessed', () => {
    it('deve marcar evento como processed', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}'
      });

      // Act
      const updatedEvent = outboxRepository.markAsProcessed(event.id);

      // Assert
      expect(updatedEvent).toBeDefined();
      expect(updatedEvent!.status).toBe('processed');
      expect(updatedEvent!.processed_at).toBeDefined();
    });

    it('deve retornar undefined para ID inexistente', () => {
      // Act
      const result = outboxRepository.markAsProcessed(999);

      // Assert
      expect(result).toBeUndefined();
    });
  });

  describe('resetFailedEvent', () => {
    it('deve resetar evento failed para pending', () => {
      // Arrange
      const event = outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'test-uuid',
        payload: '{}',
        status: 'failed',
        retry_count: 2,
        error_message: 'Previous error',
        last_retry_at: new Date().toISOString()
      });

      // Act
      const resetEvent = outboxRepository.resetFailedEvent(event.id);

      // Assert
      expect(resetEvent).toBeDefined();
      expect(resetEvent!.status).toBe('pending');
      expect(resetEvent!.error_message).toBeUndefined();
      expect(resetEvent!.last_retry_at).toBeUndefined();
    });
  });

  describe('findByStatus', () => {
    it('deve encontrar eventos por status', () => {
      // Arrange
      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 1,
        external_id: 'pending-1',
        payload: '{}',
        status: 'pending'
      });

      outboxRepository.create({
        event_type: 'payment_created',
        aggregate_id: 2,
        external_id: 'processed-1',
        payload: '{}',
        status: 'processed'
      });

      // Act
      const pendingEvents = outboxRepository.findByStatus('pending', 10);

      // Assert
      expect(pendingEvents).toHaveLength(1);
      expect(pendingEvents[0].status).toBe('pending');
      expect(pendingEvents[0].external_id).toBe('pending-1');
    });

    it('deve respeitar limite de resultados', () => {
      // Arrange
      for (let i = 1; i <= 5; i++) {
        outboxRepository.create({
          event_type: 'payment_created',
          aggregate_id: i,
          external_id: `pending-${i}`,
          payload: '{}',
          status: 'pending'
        });
      }

      // Act
      const events = outboxRepository.findByStatus('pending', 3);

      // Assert
      expect(events).toHaveLength(3);
    });
  });
});
