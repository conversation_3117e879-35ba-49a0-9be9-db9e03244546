import { vi } from 'vitest';

// Mock para PaymentExternalService
export const createExternalApiMock = () => {
  return {
    // Mock de sucesso (201)
    mockSuccess: () => {
      return vi.fn().mockResolvedValue({
        status: 201,
        statusText: 'Created',
        'id-externo': 'test-uuid'
      });
    },

    // Mock de erro 429 (Too Many Requests)
    mockTooManyRequests: () => {
      return vi.fn().mockRejectedValue(new Error('API returned status 429: 429 - Too Many Requests'));
    },

    // Mock de erro 500 (Internal Server Error)
    mockServerError: () => {
      return vi.fn().mockRejectedValue(new Error('API returned status 500: Internal Server Error'));
    },

    // Mock de timeout
    mockTimeout: () => {
      return vi.fn().mockRejectedValue(new Error('Request timeout'));
    },

    // Mock customizável
    mockCustom: (response: any) => {
      if (response instanceof Error) {
        return vi.fn().mockRejectedValue(response);
      }
      return vi.fn().mockResolvedValue(response);
    }
  };
};
