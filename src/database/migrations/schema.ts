import { DatabaseConnection } from '../connection/database-connection';

export class DatabaseSchema {
  private db: DatabaseConnection;

  constructor() {
    this.db = DatabaseConnection.getInstance();
  }

  public initializeDatabase(): void {
    this.createTables();
    this.createTriggers();
    this.insertDefaultData();
  }

  private createTables(): void {
    this.createPagamentosTable();
    this.createPedidosTable();
    this.createMeiosPagamentoTable();
  }

  private createPagamentosTable(): void {
    const createPagamentosTable = `
      CREATE TABLE IF NOT EXISTS pagamentos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        valor REAL NOT NULL,
        descricao TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pendente',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    this.db.exec(createPagamentosTable);
  }

  private createPedidosTable(): void {
    const createPedidosTable = `
      CREATE TABLE IF NOT EXISTS pedidos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cliente_nome TEXT NOT NULL,
        cliente_email TEXT NOT NULL,
        valor_total REAL NOT NULL,
        status TEXT NOT NULL DEFAULT 'pendente',
        observacoes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    this.db.exec(createPedidosTable);
  }

  private createMeiosPagamentoTable(): void {
    const createMeiosPagamentoTable = `
      CREATE TABLE IF NOT EXISTS meios_pagamento (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tipo TEXT NOT NULL CHECK (tipo IN ('pix', 'boleto', 'credito')),
        nome TEXT NOT NULL,
        descricao TEXT,
        ativo BOOLEAN NOT NULL DEFAULT 1,
        configuracao TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    this.db.exec(createMeiosPagamentoTable);
  }

  private createTriggers(): void {
    this.createPagamentosTrigger();
    this.createPedidosTrigger();
    this.createMeiosPagamentoTrigger();
  }

  private createPagamentosTrigger(): void {
    const createTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_pagamentos_updated_at
      AFTER UPDATE ON pagamentos
      FOR EACH ROW
      BEGIN
        UPDATE pagamentos SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;
    this.db.exec(createTrigger);
  }

  private createPedidosTrigger(): void {
    const createPedidosTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_pedidos_updated_at
      AFTER UPDATE ON pedidos
      FOR EACH ROW
      BEGIN
        UPDATE pedidos SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;
    this.db.exec(createPedidosTrigger);
  }

  private createMeiosPagamentoTrigger(): void {
    const createMeiosPagamentoTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_meios_pagamento_updated_at
      AFTER UPDATE ON meios_pagamento
      FOR EACH ROW
      BEGIN
        UPDATE meios_pagamento SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;
    this.db.exec(createMeiosPagamentoTrigger);
  }

  private insertDefaultData(): void {
    this.insertDefaultMeiosPagamento();
  }

  private insertDefaultMeiosPagamento(): void {
    const count = this.db.count('meios_pagamento');

    if (count === 0) {
      const insertStmt = this.db.prepare(`
        INSERT INTO meios_pagamento (tipo, nome, descricao, ativo) VALUES (?, ?, ?, ?)
      `);

      insertStmt.run('pix', 'PIX', 'Pagamento instantâneo via PIX', 1);
      insertStmt.run('boleto', 'Boleto Bancário', 'Pagamento via boleto bancário', 1);
      insertStmt.run('credito', 'Cartão de Crédito', 'Pagamento via cartão de crédito', 1);
    }
  }
}
