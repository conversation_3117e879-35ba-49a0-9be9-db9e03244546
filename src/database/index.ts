import { DatabaseSchema } from './migrations/schema';
import { PagamentoRepository } from './repositories/pagamento-repository';
import { PedidoRepository } from './repositories/pedido-repository';
import { MeioPagamentoRepository } from './repositories/meio-pagamento-repository';
import { OutboxRepository } from './repositories/outbox-repository';
import { DatabaseConnection } from './connection/database-connection';

// Inicializar o banco de dados
const schema = new DatabaseSchema();
schema.initializeDatabase();

// Criar instâncias dos repositories (singleton pattern)
export const pagamentoRepository = new PagamentoRepository();
export const pedidoRepository = new PedidoRepository();
export const meioPagamentoRepository = new MeioPagamentoRepository();
export const outboxRepository = new OutboxRepository();

// Exportar conexão para casos especiais
export const databaseConnection = DatabaseConnection.getInstance();

// Exportar tipos
export * from './types';

// Exportar classes para casos onde seja necessário criar novas instâncias
export { PagamentoRepository, PedidoRepository, MeioPagamentoRepository, OutboxRepository };
export { DatabaseConnection };
export { DatabaseSchema };

// Função para fechar a conexão (útil para testes ou shutdown da aplicação)
export const closeDatabaseConnection = (): void => {
  databaseConnection.close();
};

// Manter compatibilidade com o código antigo (deprecated)
export const databaseManager: any = {
  // Métodos de pagamento
  getAllPagamentos: () => pagamentoRepository.findAll(),
  getPagamentoById: (id: number) => pagamentoRepository.findById(id),
  createPagamento: (valor: number, descricao: string) => 
    pagamentoRepository.create({ valor, descricao, status: 'pendente' }),
  updatePagamento: (id: number, updates: any) => pagamentoRepository.update(id, updates),
  deletePagamento: (id: number) => pagamentoRepository.deleteById(id),

  // Métodos de pedido
  getAllPedidos: () => pedidoRepository.findAll(),
  getPedidoById: (id: number) => pedidoRepository.findById(id),
  createPedido: (cliente_nome: string, cliente_email: string, valor_total: number, observacoes?: string) =>
    pedidoRepository.create({ cliente_nome, cliente_email, valor_total, observacoes, status: 'pendente' }),
  updatePedido: (id: number, updates: any) => pedidoRepository.update(id, updates),
  deletePedido: (id: number) => pedidoRepository.deleteById(id),

  // Métodos de meio de pagamento
  getAllMeiosPagamento: () => meioPagamentoRepository.findAll(),
  getMeiosPagamentoAtivos: () => meioPagamentoRepository.findAtivos(),
  getMeioPagamentoById: (id: number) => meioPagamentoRepository.findById(id),
  createMeioPagamento: (tipo: any, nome: string, descricao?: string, configuracao?: string) =>
    meioPagamentoRepository.create({ tipo, nome, descricao, configuracao, ativo: true }),
  updateMeioPagamento: (id: number, updates: any) => meioPagamentoRepository.update(id, updates),
  deleteMeioPagamento: (id: number) => meioPagamentoRepository.deleteById(id),

  // Métodos de utilidade
  close: () => closeDatabaseConnection(),
  getDatabase: () => databaseConnection.getDatabase()
};

export default databaseManager;
