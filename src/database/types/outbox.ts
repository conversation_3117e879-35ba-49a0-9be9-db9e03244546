export interface OutboxEvent {
  id: number;
  event_type: string;
  aggregate_id: number;
  external_id: string; // GUID para idempotência
  payload: string; // JSON string
  status: 'pending' | 'processed' | 'failed' | 'error';
  created_at: string;
  processed_at?: string;
  retry_count: number;
  error_message?: string; // Mensagem de erro detalhada
  last_retry_at?: string; // Timestamp da última tentativa de retry
}

export type CreateOutboxEventData = Omit<OutboxEvent, 'id' | 'created_at' | 'processed_at'>;
export type UpdateOutboxEventData = Partial<Omit<OutboxEvent, 'id' | 'created_at'>>;

export interface OutboxEventPayload {
  id: number;
  valor: number;
  descricao: string;
  status: string;
  created_at: string;
  updated_at: string;
  'id-externo': string; // GUID para API externa
}
