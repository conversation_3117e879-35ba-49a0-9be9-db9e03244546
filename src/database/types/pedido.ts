export interface Pedido {
  id: number;
  cliente_nome: string;
  cliente_email: string;
  valor_total: number;
  status: 'pendente' | 'confirmado' | 'cancelado' | 'entregue';
  observacoes?: string;
  created_at: string;
  updated_at: string;
}

export type CreatePedidoData = Omit<Pedido, 'id' | 'created_at' | 'updated_at'>;
export type UpdatePedidoData = Partial<Omit<Pedido, 'id' | 'created_at' | 'updated_at'>>;
