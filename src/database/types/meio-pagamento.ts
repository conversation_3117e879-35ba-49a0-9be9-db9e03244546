export interface MeioPagamento {
  id: number;
  tipo: 'pix' | 'boleto' | 'credito';
  nome: string;
  descricao?: string;
  ativo: boolean;
  configuracao?: string; // JSON string para configurações específicas
  created_at: string;
  updated_at: string;
}

export type CreateMeioPagamentoData = Omit<MeioPagamento, 'id' | 'created_at' | 'updated_at'>;
export type UpdateMeioPagamentoData = Partial<Omit<MeioPagamento, 'id' | 'created_at' | 'updated_at'>>;
