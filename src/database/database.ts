import Database from 'better-sqlite3';
import { join } from 'path';

export interface Pagamento {
  id: number;
  valor: number;
  descricao: string;
  status: 'pendente' | 'processado' | 'cancelado';
  created_at: string;
  updated_at: string;
}

export interface Pedido {
  id: number;
  cliente_nome: string;
  cliente_email: string;
  valor_total: number;
  status: 'pendente' | 'confirmado' | 'cancelado' | 'entregue';
  observacoes?: string;
  created_at: string;
  updated_at: string;
}

export interface MeioPagamento {
  id: number;
  tipo: 'pix' | 'boleto' | 'credito';
  nome: string;
  descricao?: string;
  ativo: boolean;
  configuracao?: string; // JSON string para configurações específicas
  created_at: string;
  updated_at: string;
}

class DatabaseManager {
  private db: Database.Database;

  constructor() {
    const dbPath = join(process.cwd(), 'database.sqlite');
    this.db = new Database(dbPath);
    this.initializeTables();
  }

  private initializeTables(): void {
    // Criar tabela de pagamentos
    const createPagamentosTable = `
      CREATE TABLE IF NOT EXISTS pagamentos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        valor REAL NOT NULL,
        descricao TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pendente',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    this.db.exec(createPagamentosTable);

    // Criar trigger para atualizar updated_at
    const createTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_pagamentos_updated_at
      AFTER UPDATE ON pagamentos
      FOR EACH ROW
      BEGIN
        UPDATE pagamentos SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;

    this.db.exec(createTrigger);

    // Criar tabela de pedidos
    const createPedidosTable = `
      CREATE TABLE IF NOT EXISTS pedidos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cliente_nome TEXT NOT NULL,
        cliente_email TEXT NOT NULL,
        valor_total REAL NOT NULL,
        status TEXT NOT NULL DEFAULT 'pendente',
        observacoes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    this.db.exec(createPedidosTable);

    // Criar trigger para atualizar updated_at na tabela pedidos
    const createPedidosTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_pedidos_updated_at
      AFTER UPDATE ON pedidos
      FOR EACH ROW
      BEGIN
        UPDATE pedidos SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;

    this.db.exec(createPedidosTrigger);

    // Criar tabela de meios de pagamento
    const createMeiosPagamentoTable = `
      CREATE TABLE IF NOT EXISTS meios_pagamento (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tipo TEXT NOT NULL CHECK (tipo IN ('pix', 'boleto', 'credito')),
        nome TEXT NOT NULL,
        descricao TEXT,
        ativo BOOLEAN NOT NULL DEFAULT 1,
        configuracao TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    this.db.exec(createMeiosPagamentoTable);

    // Criar trigger para atualizar updated_at na tabela meios_pagamento
    const createMeiosPagamentoTrigger = `
      CREATE TRIGGER IF NOT EXISTS update_meios_pagamento_updated_at
      AFTER UPDATE ON meios_pagamento
      FOR EACH ROW
      BEGIN
        UPDATE meios_pagamento SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `;

    this.db.exec(createMeiosPagamentoTrigger);

    // Inserir meios de pagamento padrão se não existirem
    this.insertDefaultMeiosPagamento();
  }

  private insertDefaultMeiosPagamento(): void {
    const count = this.db.prepare('SELECT COUNT(*) as count FROM meios_pagamento').get() as { count: number };

    if (count.count === 0) {
      const insertStmt = this.db.prepare(`
        INSERT INTO meios_pagamento (tipo, nome, descricao, ativo) VALUES (?, ?, ?, ?)
      `);

      insertStmt.run('pix', 'PIX', 'Pagamento instantâneo via PIX', 1);
      insertStmt.run('boleto', 'Boleto Bancário', 'Pagamento via boleto bancário', 1);
      insertStmt.run('credito', 'Cartão de Crédito', 'Pagamento via cartão de crédito', 1);
    }
  }

  public getAllPagamentos(): Pagamento[] {
    const stmt = this.db.prepare('SELECT * FROM pagamentos ORDER BY created_at DESC');
    return stmt.all() as Pagamento[];
  }

  public getPagamentoById(id: number): Pagamento | undefined {
    const stmt = this.db.prepare('SELECT * FROM pagamentos WHERE id = ?');
    return stmt.get(id) as Pagamento | undefined;
  }

  public createPagamento(valor: number, descricao: string): Pagamento {
    const stmt = this.db.prepare(
      'INSERT INTO pagamentos (valor, descricao) VALUES (?, ?) RETURNING *'
    );
    return stmt.get(valor, descricao) as Pagamento;
  }

  public updatePagamento(id: number, updates: Partial<Omit<Pagamento, 'id' | 'created_at' | 'updated_at'>>): Pagamento | undefined {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    
    if (fields.length === 0) return this.getPagamentoById(id);

    const stmt = this.db.prepare(
      `UPDATE pagamentos SET ${fields} WHERE id = ? RETURNING *`
    );
    return stmt.get(...values, id) as Pagamento | undefined;
  }

  public deletePagamento(id: number): boolean {
    const stmt = this.db.prepare('DELETE FROM pagamentos WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  public close(): void {
    this.db.close();
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  // Métodos CRUD para Pedidos
  public getAllPedidos(): Pedido[] {
    const stmt = this.db.prepare('SELECT * FROM pedidos ORDER BY created_at DESC');
    return stmt.all() as Pedido[];
  }

  public getPedidoById(id: number): Pedido | undefined {
    const stmt = this.db.prepare('SELECT * FROM pedidos WHERE id = ?');
    return stmt.get(id) as Pedido | undefined;
  }

  public createPedido(cliente_nome: string, cliente_email: string, valor_total: number, observacoes?: string): Pedido {
    const stmt = this.db.prepare(
      'INSERT INTO pedidos (cliente_nome, cliente_email, valor_total, observacoes) VALUES (?, ?, ?, ?) RETURNING *'
    );
    return stmt.get(cliente_nome, cliente_email, valor_total, observacoes || null) as Pedido;
  }

  public updatePedido(id: number, updates: Partial<Omit<Pedido, 'id' | 'created_at' | 'updated_at'>>): Pedido | undefined {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);

    if (fields.length === 0) return this.getPedidoById(id);

    const stmt = this.db.prepare(
      `UPDATE pedidos SET ${fields} WHERE id = ? RETURNING *`
    );
    return stmt.get(...values, id) as Pedido | undefined;
  }

  public deletePedido(id: number): boolean {
    const stmt = this.db.prepare('DELETE FROM pedidos WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Métodos CRUD para Meios de Pagamento
  public getAllMeiosPagamento(): MeioPagamento[] {
    const stmt = this.db.prepare('SELECT * FROM meios_pagamento ORDER BY nome');
    return stmt.all() as MeioPagamento[];
  }

  public getMeiosPagamentoAtivos(): MeioPagamento[] {
    const stmt = this.db.prepare('SELECT * FROM meios_pagamento WHERE ativo = 1 ORDER BY nome');
    return stmt.all() as MeioPagamento[];
  }

  public getMeioPagamentoById(id: number): MeioPagamento | undefined {
    const stmt = this.db.prepare('SELECT * FROM meios_pagamento WHERE id = ?');
    return stmt.get(id) as MeioPagamento | undefined;
  }

  public createMeioPagamento(tipo: 'pix' | 'boleto' | 'credito', nome: string, descricao?: string, configuracao?: string): MeioPagamento {
    const stmt = this.db.prepare(
      'INSERT INTO meios_pagamento (tipo, nome, descricao, configuracao) VALUES (?, ?, ?, ?) RETURNING *'
    );
    return stmt.get(tipo, nome, descricao || null, configuracao || null) as MeioPagamento;
  }

  public updateMeioPagamento(id: number, updates: Partial<Omit<MeioPagamento, 'id' | 'created_at' | 'updated_at'>>): MeioPagamento | undefined {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates).map(value => {
      // Converter boolean para número para compatibilidade com SQLite
      if (typeof value === 'boolean') {
        return value ? 1 : 0;
      }
      return value;
    });

    if (fields.length === 0) return this.getMeioPagamentoById(id);

    const stmt = this.db.prepare(
      `UPDATE meios_pagamento SET ${fields} WHERE id = ? RETURNING *`
    );
    return stmt.get(...values, id) as MeioPagamento | undefined;
  }

  public deleteMeioPagamento(id: number): boolean {
    const stmt = this.db.prepare('DELETE FROM meios_pagamento WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }
}

export const databaseManager = new DatabaseManager();
export default databaseManager;
