import Database from 'better-sqlite3';
import { join } from 'path';

export class DatabaseConnection {
  private static instance: DatabaseConnection;
  private db: Database.Database;

  private constructor() {
    const dbPath = join(process.cwd(), 'database.sqlite');
    this.db = new Database(dbPath);
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  public prepare(sql: string): Database.Statement {
    return this.db.prepare(sql);
  }

  public exec(sql: string): void {
    this.db.exec(sql);
  }

  public transaction<T>(fn: () => T): T {
    const transaction = this.db.transaction(fn);
    return transaction();
  }

  public close(): void {
    if (this.db) {
      this.db.close();
    }
  }

  // Métodos utilitários para operações comuns
  public findById<T>(tableName: string, id: number): T | undefined {
    const stmt = this.prepare(`SELECT * FROM ${tableName} WHERE id = ?`);
    return stmt.get(id) as T | undefined;
  }

  public findAll<T>(tableName: string, orderBy: string = 'id'): T[] {
    const stmt = this.prepare(`SELECT * FROM ${tableName} ORDER BY ${orderBy}`);
    return stmt.all() as T[];
  }

  public deleteById(tableName: string, id: number): boolean {
    const stmt = this.prepare(`DELETE FROM ${tableName} WHERE id = ?`);
    const result = stmt.run(id);
    return result.changes > 0;
  }

  public count(tableName: string, whereClause?: string): number {
    const sql = whereClause 
      ? `SELECT COUNT(*) as count FROM ${tableName} WHERE ${whereClause}`
      : `SELECT COUNT(*) as count FROM ${tableName}`;
    const stmt = this.prepare(sql);
    const result = stmt.get() as { count: number };
    return result.count;
  }
}
