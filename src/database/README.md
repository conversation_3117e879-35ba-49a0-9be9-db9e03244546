# Database Layer - Refatoração SOLID

Esta pasta contém a nova estrutura do banco de dados, refatorada seguindo os princípios SOLID, DRY e KISS.

## Estrutura

```
src/database/
├── types/                      # Interfaces e tipos de dados
│   ├── pagamento.ts           # Interface Pagamento + tipos auxiliares
│   ├── pedido.ts              # Interface Pedido + tipos auxiliares
│   ├── meio-pagamento.ts      # Interface MeioPagamento + tipos auxiliares
│   └── index.ts               # Exporta todos os tipos
├── connection/                 # Gerenciamento de conexão
│   └── database-connection.ts  # Wrapper singleton para conexão SQLite
├── repositories/               # Repositories por entidade
│   ├── base-repository.ts     # Classe abstrata com operações comuns
│   ├── pagamento-repository.ts # Repository específico para pagamentos
│   ├── pedido-repository.ts   # Repository específico para pedidos
│   ├── meio-pagamento-repository.ts # Repository específico para meios de pagamento
│   └── index.ts               # Exporta todos os repositories
├── migrations/                 # Schema e migrations
│   └── schema.ts              # Criação de tabelas e triggers
├── index.ts                   # Ponto de entrada principal
└── README.md                  # Esta documentação
```

## Princípios Aplicados

### SOLID

- **Single Responsibility Principle (SRP)**: Cada classe tem uma única responsabilidade
  - `DatabaseConnection`: Gerencia apenas a conexão
  - `PagamentoRepository`: Apenas operações de pagamento
  - `DatabaseSchema`: Apenas criação/configuração de schema

- **Open/Closed Principle (OCP)**: Extensível via herança
  - `BaseRepository` pode ser estendido para novas entidades
  - Novos repositories podem ser criados facilmente

- **Dependency Inversion Principle (DIP)**: Dependências de abstrações
  - Repositories dependem da abstração `DatabaseConnection`
  - Não há dependências diretas de implementações concretas

### DRY (Don't Repeat Yourself)

- Operações CRUD comuns no `BaseRepository`
- Métodos utilitários reutilizáveis no `DatabaseConnection`
- Tipos auxiliares gerados automaticamente

### KISS (Keep It Simple, Stupid)

- Estrutura clara e intuitiva
- Separação lógica por responsabilidade
- Interfaces simples e bem definidas

## Como Usar

### Importação Recomendada (Nova API)

```typescript
import { 
  pagamentoRepository, 
  pedidoRepository, 
  meioPagamentoRepository 
} from '../database';

// Usar os repositories diretamente
const pagamentos = pagamentoRepository.findAll();
const pagamento = pagamentoRepository.create({ valor: 100, descricao: 'Teste' });
```

### Compatibilidade com Código Antigo

```typescript
import { databaseManager } from '../database';

// API antiga ainda funciona (deprecated)
const pagamentos = databaseManager.getAllPagamentos();
```

## Vantagens da Nova Estrutura

1. **Manutenibilidade**: Código organizado e fácil de manter
2. **Testabilidade**: Cada componente pode ser testado isoladamente
3. **Extensibilidade**: Fácil adicionar novas entidades
4. **Reutilização**: Componentes reutilizáveis
5. **Type Safety**: Melhor tipagem TypeScript
6. **Performance**: Singleton pattern para conexão
7. **Transações**: Suporte nativo a transações

## Migração

O código antigo continua funcionando através do `databaseManager` exportado no `index.ts`. 
Para migrar gradualmente:

1. Substitua imports de `../database/database` por `../database`
2. Use os repositories específicos em vez do `databaseManager`
3. Aproveite os novos métodos específicos de cada repository
