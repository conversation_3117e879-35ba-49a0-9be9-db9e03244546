import { DatabaseConnection } from '../connection/database-connection';

export abstract class BaseRepository<T, CreateData, UpdateData> {
  protected db: DatabaseConnection;
  protected abstract tableName: string;

  constructor() {
    this.db = DatabaseConnection.getInstance();
  }

  public findById(id: number): T | undefined {
    return this.db.findById<T>(this.tableName, id);
  }

  public findAll(orderBy: string = 'created_at DESC'): T[] {
    return this.db.findAll<T>(this.tableName, orderBy);
  }

  public deleteById(id: number): boolean {
    return this.db.deleteById(this.tableName, id);
  }

  public count(): number {
    return this.db.count(this.tableName);
  }

  // Métodos abstratos que devem ser implementados pelas classes filhas
  public abstract create(data: CreateData): T;
  public abstract update(id: number, data: UpdateData): T | undefined;

  // Método utilitário para construir queries de update dinamicamente
  protected buildUpdateQuery(updates: Record<string, any>): { fields: string; values: any[] } {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates).map(value => {
      // Converter boolean para número para compatibilidade com SQLite
      if (typeof value === 'boolean') {
        return value ? 1 : 0;
      }
      return value;
    });
    
    return { fields, values };
  }

  // Método utilitário para executar transações
  protected executeTransaction<R>(fn: () => R): R {
    return this.db.transaction(fn);
  }
}
