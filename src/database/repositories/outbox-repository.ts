import { BaseRepository } from './base-repository';
import { OutboxEvent, CreateOutboxEventData, UpdateOutboxEventData } from '../types/outbox';

export class OutboxRepository extends BaseRepository<OutboxEvent, CreateOutboxEventData, UpdateOutboxEventData> {
  protected tableName = 'outbox';

  public create(data: CreateOutboxEventData): OutboxEvent {
    const stmt = this.db.prepare(
      'INSERT INTO outbox (event_type, aggregate_id, external_id, payload, status, retry_count, error_message, last_retry_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *'
    );
    return stmt.get(
      data.event_type,
      data.aggregate_id,
      data.external_id,
      data.payload,
      data.status || 'pending',
      data.retry_count || 0,
      data.error_message || null,
      data.last_retry_at || null
    ) as OutboxEvent;
  }

  public update(id: number, updates: UpdateOutboxEventData): OutboxEvent | undefined {
    if (Object.keys(updates).length === 0) {
      return this.findById(id);
    }

    const { fields, values } = this.buildUpdateQuery(updates);
    const stmt = this.db.prepare(
      `UPDATE outbox SET ${fields} WHERE id = ? RETURNING *`
    );
    return stmt.get(...values, id) as OutboxEvent | undefined;
  }

  // Métodos específicos para outbox
  public findPendingEvents(limit: number = 10): OutboxEvent[] {
    const stmt = this.db.prepare(
      'SELECT * FROM outbox WHERE status = ? ORDER BY created_at ASC LIMIT ?'
    );
    return stmt.all('pending', limit) as OutboxEvent[];
  }

  public findByExternalId(externalId: string): OutboxEvent | undefined {
    const stmt = this.db.prepare('SELECT * FROM outbox WHERE external_id = ?');
    return stmt.get(externalId) as OutboxEvent | undefined;
  }

  public markAsProcessed(id: number): OutboxEvent | undefined {
    return this.update(id, {
      status: 'processed',
      processed_at: new Date().toISOString()
    });
  }

  public markAsFailed(id: number, retryCount: number, errorMessage?: string): OutboxEvent | undefined {
    const status = retryCount >= 3 ? 'error' : 'failed';
    return this.update(id, {
      status,
      retry_count: retryCount,
      error_message: errorMessage,
      last_retry_at: new Date().toISOString()
    });
  }

  public resetFailedEvent(id: number): OutboxEvent | undefined {
    return this.update(id, {
      status: 'pending',
      error_message: undefined,
      last_retry_at: undefined
    });
  }

  public findFailedEvents(): OutboxEvent[] {
    const stmt = this.db.prepare(
      'SELECT * FROM outbox WHERE status = ? ORDER BY created_at ASC'
    );
    return stmt.all('failed') as OutboxEvent[];
  }

  public findErrorEvents(): OutboxEvent[] {
    const stmt = this.db.prepare(
      'SELECT * FROM outbox WHERE status = ? ORDER BY created_at ASC'
    );
    return stmt.all('error') as OutboxEvent[];
  }

  public getStatistics(): { pending: number; processed: number; failed: number; error: number } {
    const stmt = this.db.prepare(`
      SELECT 
        status,
        COUNT(*) as count
      FROM outbox 
      GROUP BY status
    `);
    
    const results = stmt.all() as Array<{ status: string; count: number }>;
    
    const stats = { pending: 0, processed: 0, failed: 0, error: 0 };
    results.forEach(result => {
      if (result.status in stats) {
        (stats as any)[result.status] = result.count;
      }
    });
    
    return stats;
  }
}
