import { BaseRepository } from './base-repository';
import { Pagamento, CreatePagamentoData, UpdatePagamentoData } from '../types/pagamento';

export class PagamentoRepository extends BaseRepository<Pagamento, CreatePagamentoData, UpdatePagamentoData> {
  protected tableName = 'pagamentos';

  public create(data: CreatePagamentoData): Pagamento {
    const stmt = this.db.prepare(
      'INSERT INTO pagamentos (valor, descricao, status) VALUES (?, ?, ?) RETURNING *'
    );
    return stmt.get(data.valor, data.descricao, data.status || 'pendente') as Pagamento;
  }

  public update(id: number, updates: UpdatePagamentoData): Pagamento | undefined {
    if (Object.keys(updates).length === 0) {
      return this.findById(id);
    }

    const { fields, values } = this.buildUpdateQuery(updates);
    const stmt = this.db.prepare(
      `UPDATE pagamentos SET ${fields} WHERE id = ? RETURNING *`
    );
    return stmt.get(...values, id) as Pagamento | undefined;
  }

  // Métodos específicos para pagamentos
  public findByStatus(status: Pagamento['status']): Pagamento[] {
    const stmt = this.db.prepare('SELECT * FROM pagamentos WHERE status = ? ORDER BY created_at DESC');
    return stmt.all(status) as Pagamento[];
  }

  public findByValueRange(minValue: number, maxValue: number): Pagamento[] {
    const stmt = this.db.prepare(
      'SELECT * FROM pagamentos WHERE valor BETWEEN ? AND ? ORDER BY created_at DESC'
    );
    return stmt.all(minValue, maxValue) as Pagamento[];
  }

  public updateStatus(id: number, status: Pagamento['status']): Pagamento | undefined {
    return this.update(id, { status });
  }

  public getTotalByStatus(status: Pagamento['status']): number {
    const stmt = this.db.prepare('SELECT COALESCE(SUM(valor), 0) as total FROM pagamentos WHERE status = ?');
    const result = stmt.get(status) as { total: number };
    return result.total;
  }
}
