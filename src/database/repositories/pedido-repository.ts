import { BaseRepository } from './base-repository';
import { Pedido, CreatePedidoData, UpdatePedidoData } from '../types/pedido';

export class PedidoRepository extends BaseRepository<Pedido, CreatePedidoData, UpdatePedidoData> {
  protected tableName = 'pedidos';

  public create(data: CreatePedidoData): Pedido {
    const stmt = this.db.prepare(
      'INSERT INTO pedidos (cliente_nome, cliente_email, valor_total, status, observacoes) VALUES (?, ?, ?, ?, ?) RETURNING *'
    );
    return stmt.get(
      data.cliente_nome,
      data.cliente_email,
      data.valor_total,
      data.status || 'pendente',
      data.observacoes || null
    ) as Pedido;
  }

  public update(id: number, updates: UpdatePedidoData): Pedido | undefined {
    if (Object.keys(updates).length === 0) {
      return this.findById(id);
    }

    const { fields, values } = this.buildUpdateQuery(updates);
    const stmt = this.db.prepare(
      `UPDATE pedidos SET ${fields} WHERE id = ? RETURNING *`
    );
    return stmt.get(...values, id) as Pedido | undefined;
  }

  // Métodos específicos para pedidos
  public findByStatus(status: Pedido['status']): Pedido[] {
    const stmt = this.db.prepare('SELECT * FROM pedidos WHERE status = ? ORDER BY created_at DESC');
    return stmt.all(status) as Pedido[];
  }

  public findByClienteEmail(email: string): Pedido[] {
    const stmt = this.db.prepare('SELECT * FROM pedidos WHERE cliente_email = ? ORDER BY created_at DESC');
    return stmt.all(email) as Pedido[];
  }

  public findByClienteNome(nome: string): Pedido[] {
    const stmt = this.db.prepare('SELECT * FROM pedidos WHERE cliente_nome LIKE ? ORDER BY created_at DESC');
    return stmt.all(`%${nome}%`) as Pedido[];
  }

  public findByValueRange(minValue: number, maxValue: number): Pedido[] {
    const stmt = this.db.prepare(
      'SELECT * FROM pedidos WHERE valor_total BETWEEN ? AND ? ORDER BY created_at DESC'
    );
    return stmt.all(minValue, maxValue) as Pedido[];
  }

  public updateStatus(id: number, status: Pedido['status']): Pedido | undefined {
    return this.update(id, { status });
  }

  public getTotalByStatus(status: Pedido['status']): number {
    const stmt = this.db.prepare('SELECT COALESCE(SUM(valor_total), 0) as total FROM pedidos WHERE status = ?');
    const result = stmt.get(status) as { total: number };
    return result.total;
  }

  public getClienteStats(): Array<{ cliente_email: string; total_pedidos: number; valor_total: number }> {
    const stmt = this.db.prepare(`
      SELECT 
        cliente_email,
        COUNT(*) as total_pedidos,
        SUM(valor_total) as valor_total
      FROM pedidos 
      GROUP BY cliente_email 
      ORDER BY valor_total DESC
    `);
    return stmt.all() as Array<{ cliente_email: string; total_pedidos: number; valor_total: number }>;
  }
}
