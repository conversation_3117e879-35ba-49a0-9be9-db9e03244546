import { BaseRepository } from './base-repository';
import { MeioPagamento, CreateMeioPagamentoData, UpdateMeioPagamentoData } from '../types/meio-pagamento';

export class MeioPagamentoRepository extends BaseRepository<MeioPagamento, CreateMeioPagamentoData, UpdateMeioPagamentoData> {
  protected tableName = 'meios_pagamento';

  public create(data: CreateMeioPagamentoData): MeioPagamento {
    const stmt = this.db.prepare(
      'INSERT INTO meios_pagamento (tipo, nome, descricao, ativo, configuracao) VALUES (?, ?, ?, ?, ?) RETURNING *'
    );
    return stmt.get(
      data.tipo,
      data.nome,
      data.descricao || null,
      data.ativo !== undefined ? (data.ativo ? 1 : 0) : 1,
      data.configuracao || null
    ) as MeioPagamento;
  }

  public update(id: number, updates: UpdateMeioPagamentoData): MeioPagamento | undefined {
    if (Object.keys(updates).length === 0) {
      return this.findById(id);
    }

    const { fields, values } = this.buildUpdateQuery(updates);
    const stmt = this.db.prepare(
      `UPDATE meios_pagamento SET ${fields} WHERE id = ? RETURNING *`
    );
    return stmt.get(...values, id) as MeioPagamento | undefined;
  }

  // Métodos específicos para meios de pagamento
  public findAll(orderBy: string = 'nome'): MeioPagamento[] {
    return super.findAll(orderBy);
  }

  public findAtivos(): MeioPagamento[] {
    const stmt = this.db.prepare('SELECT * FROM meios_pagamento WHERE ativo = 1 ORDER BY nome');
    return stmt.all() as MeioPagamento[];
  }

  public findByTipo(tipo: MeioPagamento['tipo']): MeioPagamento[] {
    const stmt = this.db.prepare('SELECT * FROM meios_pagamento WHERE tipo = ? ORDER BY nome');
    return stmt.all(tipo) as MeioPagamento[];
  }

  public findAtivosByTipo(tipo: MeioPagamento['tipo']): MeioPagamento[] {
    const stmt = this.db.prepare('SELECT * FROM meios_pagamento WHERE tipo = ? AND ativo = 1 ORDER BY nome');
    return stmt.all(tipo) as MeioPagamento[];
  }

  public activate(id: number): MeioPagamento | undefined {
    return this.update(id, { ativo: true });
  }

  public deactivate(id: number): MeioPagamento | undefined {
    return this.update(id, { ativo: false });
  }

  public updateConfiguracao(id: number, configuracao: string): MeioPagamento | undefined {
    return this.update(id, { configuracao });
  }

  public countByTipo(): Array<{ tipo: string; count: number }> {
    const stmt = this.db.prepare(`
      SELECT tipo, COUNT(*) as count 
      FROM meios_pagamento 
      GROUP BY tipo 
      ORDER BY tipo
    `);
    return stmt.all() as Array<{ tipo: string; count: number }>;
  }

  public countAtivos(): number {
    return this.db.count(this.tableName, 'ativo = 1');
  }
}
