import Fastify, { FastifyInstance } from 'fastify';
import { healthRoutes } from './routes/health';
import { pagamentosRoutes } from './routes/pagamentos';
import { pedidosRoutes } from './routes/pedidos';
import { meiosPagamentoRoutes } from './routes/meios-pagamento';
import { outboxRoutes } from './routes/outbox';
import { BackgroundWorker } from './services/background-worker';

const PORT = process.env.PORT ? parseInt(process.env.PORT) : 3000;
const HOST = process.env.HOST || '0.0.0.0';

// Instância global do background worker
let backgroundWorker: BackgroundWorker;

async function buildApp(): Promise<FastifyInstance> {
  const fastify = Fastify({
    logger: {
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname'
        }
      }
    }
  });

  // Registrar plugin do Swagger
  await fastify.register(require('@fastify/swagger'), {
    swagger: {
      info: {
        title: 'API de Pagamentos',
        description: 'API para gerenciamento de pagamentos com padrão Outbox',
        version: '1.0.0'
      },
      schemes: ['http'],
      consumes: ['application/json'],
      produces: ['application/json'],
      tags: [
        { name: 'Health', description: 'Health check endpoints' },
        { name: 'Pagamentos', description: 'Endpoints para gerenciamento de pagamentos' },
        { name: 'Pedidos', description: 'Endpoints para gerenciamento de pedidos' },
        { name: 'Meios de Pagamento', description: 'Endpoints para gerenciamento de meios de pagamento' },
        { name: 'Outbox', description: 'Endpoints para monitoramento da outbox (Transactional Outbox Pattern)' }
      ]
    }
  });

  // Registrar plugin do Swagger UI
  await fastify.register(require('@fastify/swagger-ui'), {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'full',
      deepLinking: false
    }
  });

  // Registrar rotas
  await fastify.register(healthRoutes);
  await fastify.register(pagamentosRoutes);
  await fastify.register(pedidosRoutes);
  await fastify.register(meiosPagamentoRoutes);
  await fastify.register(outboxRoutes);

  // Hook para log de requisições
  fastify.addHook('onRequest', async (request, reply) => {
    fastify.log.info(`${request.method} ${request.url}`);
  });

  // Hook para tratamento de erros
  fastify.setErrorHandler((error, request, reply) => {
    fastify.log.error(error);
    
    if (error.validation) {
      reply.status(400).send({
        error: 'Validation Error',
        message: 'Dados de entrada inválidos',
        details: error.validation
      });
      return;
    }

    reply.status(500).send({
      error: 'Internal Server Error',
      message: 'Erro interno do servidor'
    });
  });

  return fastify;
}

async function start() {
  try {
    const app = await buildApp();

    // Iniciar background worker
    backgroundWorker = new BackgroundWorker();
    backgroundWorker.start();

    await app.listen({ port: PORT, host: HOST });

    console.log(`🚀 Servidor rodando em http://${HOST}:${PORT}`);
    console.log(`📚 Documentação disponível em http://${HOST}:${PORT}/docs`);
    console.log(`⚡ Background worker iniciado para processamento de eventos outbox`);

  } catch (err) {
    console.error('Erro ao iniciar o servidor:', err);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Recebido SIGINT, encerrando servidor...');
  if (backgroundWorker) {
    backgroundWorker.stop();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Recebido SIGTERM, encerrando servidor...');
  if (backgroundWorker) {
    backgroundWorker.stop();
  }
  process.exit(0);
});

if (require.main === module) {
  start();
}

export { buildApp };
