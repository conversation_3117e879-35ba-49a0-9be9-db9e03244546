# Types Layer - Refatoração SOLID

Esta pasta contém a estrutura de tipos refatorada seguindo os princípios SOLID, DRY e KISS.

## Estrutura

```
src/types/
├── pagamento-requests.ts      # Interfaces para requests de pagamento
├── pedido-requests.ts         # Interfaces para requests de pedido
├── meio-pagamento-requests.ts # Interfaces para requests de meio de pagamento
├── common.ts                  # Interfaces comuns (Health, Error, etc.)
├── index.ts                   # Re-exporta todos os tipos
└── README.md                  # Esta documentação
```

## Princípios Aplicados

### SOLID

- **Single Responsibility Principle (SRP)**: Cada arquivo tem uma única responsabilidade
  - `pagamento-requests.ts`: Apenas tipos relacionados a requests de pagamento
  - `pedido-requests.ts`: Apenas tipos relacionados a requests de pedido
  - `meio-pagamento-requests.ts`: Apenas tipos relacionados a requests de meio de pagamento
  - `common.ts`: Apenas tipos comuns/compartilhados

- **Open/Closed Principle (OCP)**: Extensível sem modificar código existente
  - Novos tipos podem ser adicionados em novos arquivos
  - Estrutura permite extensão fácil

### DRY (Don't Repeat Yourself)

- Tipos comuns centralizados em `common.ts`
- Evita duplicação de interfaces similares
- Re-exportação centralizada no `index.ts`

### KISS (Keep It Simple, Stupid)

- Estrutura clara e intuitiva
- Separação lógica por entidade
- Nomes descritivos e consistentes

## Arquivos

### `pagamento-requests.ts`
Contém interfaces para operações de pagamento:
- `CreatePagamentoRequest`
- `UpdatePagamentoRequest`

### `pedido-requests.ts`
Contém interfaces para operações de pedido:
- `CreatePedidoRequest`
- `UpdatePedidoRequest`

### `meio-pagamento-requests.ts`
Contém interfaces para operações de meio de pagamento:
- `CreateMeioPagamentoRequest`
- `UpdateMeioPagamentoRequest`

### `common.ts`
Contém interfaces compartilhadas:
- `HealthResponse`
- `ErrorResponse`

### `index.ts`
Re-exporta todos os tipos para manter compatibilidade:
```typescript
export * from './pagamento-requests';
export * from './pedido-requests';
export * from './meio-pagamento-requests';
export * from './common';
```

## Como Usar

### Importação (mantém compatibilidade)

```typescript
import { 
  CreatePagamentoRequest, 
  UpdatePagamentoRequest,
  HealthResponse 
} from '../types';
```

### Importação Específica (recomendado para novos códigos)

```typescript
import { CreatePagamentoRequest } from '../types/pagamento-requests';
import { HealthResponse } from '../types/common';
```

## Vantagens da Nova Estrutura

1. **Organização**: Tipos agrupados por responsabilidade
2. **Manutenibilidade**: Fácil localizar e modificar tipos específicos
3. **Escalabilidade**: Simples adicionar novos tipos sem afetar existentes
4. **Legibilidade**: Código mais limpo e organizado
5. **Compatibilidade**: Mantém funcionamento do código existente
6. **Separação de Responsabilidades**: Cada arquivo tem propósito específico

## Migração

O código existente continua funcionando sem modificações, pois o `index.ts` re-exporta todos os tipos. Para migrar gradualmente:

1. Use imports específicos em novos códigos
2. Considere agrupar tipos relacionados nos arquivos apropriados
3. Mantenha a convenção de nomenclatura estabelecida
