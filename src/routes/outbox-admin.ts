import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { OutboxService } from '../services/outbox-service';

export async function outboxAdminRoutes(fastify: FastifyInstance) {
  const outboxService = new OutboxService();

  // POST /outbox/admin/reset-failed - Resetar todos os eventos failed para pending
  fastify.post('/outbox/admin/reset-failed', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            resetCount: { type: 'number' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const failedEvents = outboxService.getFailedEvents();
      let resetCount = 0;

      for (const event of failedEvents) {
        // Resetar apenas eventos que não atingiram o máximo de tentativas
        if (event.retry_count < 3) {
          outboxService.resetFailedEvent(event.id);
          resetCount++;
          console.log(`[OutboxAdmin] Evento ${event.id} resetado para pending`);
        }
      }

      return reply.code(200).send({
        message: `${resetCount} eventos foram resetados para pending`,
        resetCount
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao resetar eventos failed'
      });
    }
  });

  // POST /outbox/admin/reset-all-errors - Resetar eventos com erro (máximo tentativas) para pending
  fastify.post('/outbox/admin/reset-all-errors', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            resetCount: { type: 'number' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const errorEvents = outboxService.getErrorEvents();
      let resetCount = 0;

      // Resetar retry_count para 0 e status para pending
      const outboxRepository = new (require('../database/repositories/outbox-repository').OutboxRepository)();
      
      for (const event of errorEvents) {
        outboxRepository.update(event.id, {
          status: 'pending',
          retry_count: 0,
          error_message: undefined,
          last_retry_at: undefined
        });
        resetCount++;
        console.log(`[OutboxAdmin] Evento ${event.id} resetado completamente (retry_count=0)`);
      }

      return reply.code(200).send({
        message: `${resetCount} eventos com erro foram resetados completamente`,
        resetCount
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao resetar eventos com erro'
      });
    }
  });
}
