import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { OutboxService } from '../services/outbox-service';
import { BackgroundWorker } from '../services/background-worker';

export async function outboxRoutes(fastify: FastifyInstance) {
  const outboxService = new OutboxService();

  // GET /outbox/statistics - Estatísticas da outbox
  fastify.get('/outbox/statistics', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            pending: { type: 'number' },
            processed: { type: 'number' },
            failed: { type: 'number' },
            error: { type: 'number' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const statistics = outboxService.getStatistics();
      return reply.code(200).send(statistics);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar estatísticas da outbox'
      });
    }
  });

  // GET /outbox/failed - Eventos que falharam
  fastify.get('/outbox/failed', {
    schema: {
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              event_type: { type: 'string' },
              aggregate_id: { type: 'number' },
              external_id: { type: 'string' },
              status: { type: 'string' },
              retry_count: { type: 'number' },
              created_at: { type: 'string', format: 'date-time' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const failedEvents = outboxService.getFailedEvents();
      return reply.code(200).send(failedEvents);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar eventos falhados'
      });
    }
  });

  // GET /outbox/errors - Eventos com erro (máximo de tentativas atingido)
  fastify.get('/outbox/errors', {
    schema: {
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              event_type: { type: 'string' },
              aggregate_id: { type: 'number' },
              external_id: { type: 'string' },
              status: { type: 'string' },
              retry_count: { type: 'number' },
              created_at: { type: 'string', format: 'date-time' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const errorEvents = outboxService.getErrorEvents();
      return reply.code(200).send(errorEvents);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar eventos com erro'
      });
    }
  });
}
