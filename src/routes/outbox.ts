import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { OutboxService } from '../services/outbox-service';
import { BackgroundWorker } from '../services/background-worker';

export async function outboxRoutes(fastify: FastifyInstance) {
  const outboxService = new OutboxService();

  // GET /outbox/statistics - Estatísticas da outbox
  fastify.get('/outbox/statistics', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            pending: { type: 'number' },
            processed: { type: 'number' },
            failed: { type: 'number' },
            error: { type: 'number' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const statistics = outboxService.getStatistics();
      return reply.code(200).send(statistics);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar estatísticas da outbox'
      });
    }
  });

  // GET /outbox/failed - Eventos que falharam
  fastify.get('/outbox/failed', {
    schema: {
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              event_type: { type: 'string' },
              aggregate_id: { type: 'number' },
              external_id: { type: 'string' },
              status: { type: 'string' },
              retry_count: { type: 'number' },
              created_at: { type: 'string', format: 'date-time' },
              error_message: { type: 'string' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const failedEvents = outboxService.getFailedEvents();
      return reply.code(200).send(failedEvents);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar eventos falhados'
      });
    }
  });

  // GET /outbox/errors - Eventos com erro (máximo de tentativas atingido)
  fastify.get('/outbox/errors', {
    schema: {
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              event_type: { type: 'string' },
              aggregate_id: { type: 'number' },
              external_id: { type: 'string' },
              status: { type: 'string' },
              retry_count: { type: 'number' },
              created_at: { type: 'string', format: 'date-time' },
              error_message: { type: 'string' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const errorEvents = outboxService.getErrorEvents();
      return reply.code(200).send(errorEvents);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar eventos com erro'
      });
    }
  });

  // GET /outbox/events/:id - Buscar evento específico por ID com detalhes completos
  fastify.get<{ Params: { id: string } }>('/outbox/events/:id', {
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'number' },
            event_type: { type: 'string' },
            aggregate_id: { type: 'number' },
            external_id: { type: 'string' },
            payload: { type: 'string' },
            status: { type: 'string' },
            retry_count: { type: 'number' },
            created_at: { type: 'string', format: 'date-time' },
            processed_at: { type: 'string', format: 'date-time' },
            error_message: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const eventId = parseInt(request.params.id);

      if (isNaN(eventId)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      // Buscar por ID numérico primeiro, depois por external_id se não for número
      let event;
      if (!isNaN(eventId)) {
        // Buscar por ID numérico usando o repository diretamente
        const outboxRepository = new (require('../database/repositories/outbox-repository').OutboxRepository)();
        event = outboxRepository.findById(eventId);
      } else {
        // Buscar por external_id
        event = outboxService.findByExternalId(request.params.id);
      }

      if (!event) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Evento não encontrado'
        });
      }

      return reply.code(200).send(event);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar evento'
      });
    }
  });
}
