import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { databaseManager } from '../database';
import { CreatePagamentoRequest, UpdatePagamentoRequest } from '../types';

interface GetPagamentoParams {
  id: string;
}

export async function pagamentosRoutes(fastify: FastifyInstance) {
  // GET /pagamentos - Listar todos os pagamentos
  fastify.get('/pagamentos', {
    schema: {
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              valor: { type: 'number' },
              descricao: { type: 'string' },
              status: { type: 'string', enum: ['pendente', 'processado', 'cancelado'] },
              created_at: { type: 'string', format: 'date-time' },
              updated_at: { type: 'string', format: 'date-time' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const pagamentos = databaseManager.getAllPagamentos();
      return reply.code(200).send(pagamentos);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar pagamentos'
      });
    }
  });

  // GET /pagamentos/:id - Buscar pagamento por ID
  fastify.get<{ Params: GetPagamentoParams }>('/pagamentos/:id', async (request: FastifyRequest<{ Params: GetPagamentoParams }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      const pagamento = databaseManager.getPagamentoById(id);
      
      if (!pagamento) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Pagamento não encontrado'
        });
      }

      return reply.code(200).send(pagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar pagamento'
      });
    }
  });

  // POST /pagamentos - Criar novo pagamento
  fastify.post<{ Body: CreatePagamentoRequest }>('/pagamentos', {
    schema: {
      body: {
        type: 'object',
        required: ['valor', 'descricao'],
        properties: {
          valor: { type: 'number', minimum: 0.01 },
          descricao: { type: 'string', minLength: 1 }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'number' },
            valor: { type: 'number' },
            descricao: { type: 'string' },
            status: { type: 'string', enum: ['pendente', 'processado', 'cancelado'] },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: CreatePagamentoRequest }>, reply: FastifyReply) => {
    try {
      const { valor, descricao } = request.body;
      const pagamento = databaseManager.createPagamento(valor, descricao);
      return reply.code(201).send(pagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao criar pagamento'
      });
    }
  });

  // PUT /pagamentos/:id - Atualizar pagamento
  fastify.put<{ Params: GetPagamentoParams; Body: UpdatePagamentoRequest }>('/pagamentos/:id', async (request: FastifyRequest<{ Params: GetPagamentoParams; Body: UpdatePagamentoRequest }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      const pagamento = databaseManager.updatePagamento(id, request.body);
      
      if (!pagamento) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Pagamento não encontrado'
        });
      }

      return reply.code(200).send(pagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao atualizar pagamento'
      });
    }
  });

  // DELETE /pagamentos/:id - Deletar pagamento
  fastify.delete<{ Params: GetPagamentoParams }>('/pagamentos/:id', async (request: FastifyRequest<{ Params: GetPagamentoParams }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      const deleted = databaseManager.deletePagamento(id);
      
      if (!deleted) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Pagamento não encontrado'
        });
      }

      return reply.code(204).send();
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao deletar pagamento'
      });
    }
  });
}
