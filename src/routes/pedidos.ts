import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { databaseManager } from '../database';
import { CreatePedidoRequest, UpdatePedidoRequest } from '../types';

interface GetPedidoParams {
  id: string;
}

export async function pedidosRoutes(fastify: FastifyInstance) {
  // GET /pedidos - Listar todos os pedidos
  fastify.get('/pedidos', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const pedidos = databaseManager.getAllPedidos();
      return reply.code(200).send(pedidos);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar pedidos'
      });
    }
  });

  // GET /pedidos/:id - Buscar pedido por ID
  fastify.get<{ Params: GetPedidoParams }>('/pedidos/:id', async (request: FastifyRequest<{ Params: GetPedidoParams }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      const pedido = databaseManager.getPedidoById(id);
      
      if (!pedido) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Pedido não encontrado'
        });
      }

      return reply.code(200).send(pedido);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar pedido'
      });
    }
  });

  // POST /pedidos - Criar novo pedido
  fastify.post<{ Body: CreatePedidoRequest }>('/pedidos', async (request: FastifyRequest<{ Body: CreatePedidoRequest }>, reply: FastifyReply) => {
    try {
      const { cliente_nome, cliente_email, valor_total, observacoes } = request.body;
      
      // Validações básicas
      if (!cliente_nome || !cliente_email || !valor_total) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'cliente_nome, cliente_email e valor_total são obrigatórios'
        });
      }

      if (valor_total <= 0) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'valor_total deve ser maior que zero'
        });
      }

      const pedido = databaseManager.createPedido(cliente_nome, cliente_email, valor_total, observacoes);
      return reply.code(201).send(pedido);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao criar pedido'
      });
    }
  });

  // PUT /pedidos/:id - Atualizar pedido
  fastify.put<{ Params: GetPedidoParams; Body: UpdatePedidoRequest }>('/pedidos/:id', async (request: FastifyRequest<{ Params: GetPedidoParams; Body: UpdatePedidoRequest }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      // Validar valor_total se fornecido
      if (request.body.valor_total !== undefined && request.body.valor_total <= 0) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'valor_total deve ser maior que zero'
        });
      }

      const pedido = databaseManager.updatePedido(id, request.body);
      
      if (!pedido) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Pedido não encontrado'
        });
      }

      return reply.code(200).send(pedido);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao atualizar pedido'
      });
    }
  });

  // DELETE /pedidos/:id - Deletar pedido
  fastify.delete<{ Params: GetPedidoParams }>('/pedidos/:id', async (request: FastifyRequest<{ Params: GetPedidoParams }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      const deleted = databaseManager.deletePedido(id);
      
      if (!deleted) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Pedido não encontrado'
        });
      }

      return reply.code(204).send();
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao deletar pedido'
      });
    }
  });
}
