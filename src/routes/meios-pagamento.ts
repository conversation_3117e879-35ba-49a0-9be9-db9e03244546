import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { databaseManager } from '../database';
import { CreateMeioPagamentoRequest, UpdateMeioPagamentoRequest } from '../types';

interface GetMeioPagamentoParams {
  id: string;
}

export async function meiosPagamentoRoutes(fastify: FastifyInstance) {
  // GET /meios-pagamento - Listar todos os meios de pagamento
  fastify.get('/meios-pagamento', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const meiosPagamento = databaseManager.getAllMeiosPagamento();
      return reply.code(200).send(meiosPagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar meios de pagamento'
      });
    }
  });

  // GET /meios-pagamento/ativos - Listar apenas meios de pagamento ativos
  fastify.get('/meios-pagamento/ativos', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const meiosPagamento = databaseManager.getMeiosPagamentoAtivos();
      return reply.code(200).send(meiosPagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar meios de pagamento ativos'
      });
    }
  });

  // GET /meios-pagamento/:id - Buscar meio de pagamento por ID
  fastify.get<{ Params: GetMeioPagamentoParams }>('/meios-pagamento/:id', async (request: FastifyRequest<{ Params: GetMeioPagamentoParams }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      const meioPagamento = databaseManager.getMeioPagamentoById(id);
      
      if (!meioPagamento) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Meio de pagamento não encontrado'
        });
      }

      return reply.code(200).send(meioPagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao buscar meio de pagamento'
      });
    }
  });

  // POST /meios-pagamento - Criar novo meio de pagamento
  fastify.post<{ Body: CreateMeioPagamentoRequest }>('/meios-pagamento', async (request: FastifyRequest<{ Body: CreateMeioPagamentoRequest }>, reply: FastifyReply) => {
    try {
      const { tipo, nome, descricao, configuracao } = request.body;
      
      // Validações básicas
      if (!tipo || !nome) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'tipo e nome são obrigatórios'
        });
      }

      if (!['pix', 'boleto', 'credito'].includes(tipo)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'tipo deve ser: pix, boleto ou credito'
        });
      }

      const meioPagamento = databaseManager.createMeioPagamento(tipo, nome, descricao, configuracao);
      return reply.code(201).send(meioPagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao criar meio de pagamento'
      });
    }
  });

  // PUT /meios-pagamento/:id - Atualizar meio de pagamento
  fastify.put<{ Params: GetMeioPagamentoParams; Body: UpdateMeioPagamentoRequest }>('/meios-pagamento/:id', async (request: FastifyRequest<{ Params: GetMeioPagamentoParams; Body: UpdateMeioPagamentoRequest }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      // Validar tipo se fornecido
      if (request.body.tipo && !['pix', 'boleto', 'credito'].includes(request.body.tipo)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'tipo deve ser: pix, boleto ou credito'
        });
      }

      const meioPagamento = databaseManager.updateMeioPagamento(id, request.body);
      
      if (!meioPagamento) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Meio de pagamento não encontrado'
        });
      }

      return reply.code(200).send(meioPagamento);
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao atualizar meio de pagamento'
      });
    }
  });

  // DELETE /meios-pagamento/:id - Deletar meio de pagamento
  fastify.delete<{ Params: GetMeioPagamentoParams }>('/meios-pagamento/:id', async (request: FastifyRequest<{ Params: GetMeioPagamentoParams }>, reply: FastifyReply) => {
    try {
      const id = parseInt(request.params.id);
      
      if (isNaN(id)) {
        return reply.code(400).send({
          error: 'Bad Request',
          message: 'ID deve ser um número válido'
        });
      }

      const deleted = databaseManager.deleteMeioPagamento(id);
      
      if (!deleted) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'Meio de pagamento não encontrado'
        });
      }

      return reply.code(204).send();
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Erro ao deletar meio de pagamento'
      });
    }
  });
}
