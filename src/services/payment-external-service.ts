import { OutboxEventPayload } from '../database/types/outbox';

export interface ExternalApiResponse {
  success: boolean;
  statusCode: number;
  data?: any;
  error?: string;
}

export class PaymentExternalService {
  private readonly apiUrl = 'https://payment-payz.free.beeceptor.com/';
  private readonly timeout = 10000; // 10 segundos

  public async sendPayment(payload: OutboxEventPayload): Promise<ExternalApiResponse> {
    try {
      console.log(`[PaymentExternalService] Enviando pagamento para API externa:`, {
        id: payload.id,
        'id-externo': payload['id-externo'],
        valor: payload.valor
      });

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log(`[PaymentExternalService] Resposta da API externa:`, {
        status: response.status,
        statusText: response.statusText,
        'id-externo': payload['id-externo']
      });

      if (response.status === 201) {
        const responseData = await response.json().catch(() => ({}));
        return {
          success: true,
          statusCode: response.status,
          data: responseData
        };
      } else {
        const errorData = await response.text().catch(() => 'Unknown error');
        return {
          success: false,
          statusCode: response.status,
          error: `API returned status ${response.status}: ${errorData}`
        };
      }

    } catch (error: any) {
      console.error(`[PaymentExternalService] Erro ao enviar pagamento:`, {
        'id-externo': payload['id-externo'],
        error: error.message
      });

      if (error.name === 'AbortError') {
        return {
          success: false,
          statusCode: 408,
          error: 'Request timeout'
        };
      }

      return {
        success: false,
        statusCode: 500,
        error: error.message || 'Unknown error occurred'
      };
    }
  }
}
