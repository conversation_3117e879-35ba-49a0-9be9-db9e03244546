import { PagamentoRepository } from '../database/repositories/pagamento-repository';
import { OutboxService } from './outbox-service';
import { DatabaseConnection } from '../database/connection/database-connection';
import { Pagamento, CreatePagamentoData } from '../database/types/pagamento';
import { OutboxEvent } from '../database/types/outbox';

export interface PaymentCreationResult {
  pagamento: Pagamento;
  outboxEvent: OutboxEvent;
}

export class PaymentService {
  private pagamentoRepository: PagamentoRepository;
  private outboxService: OutboxService;
  private db: DatabaseConnection;

  constructor() {
    this.pagamentoRepository = new PagamentoRepository();
    this.outboxService = new OutboxService();
    this.db = DatabaseConnection.getInstance();
  }

  public createPaymentWithOutbox(data: CreatePagamentoData): PaymentCreationResult {
    console.log('[PaymentService] Iniciando criação de pagamento com transação atômica');

    // Executar transação atômica
    return this.db.transaction(() => {
      try {
        // 1. Criar pagamento na base de dados
        const pagamento = this.pagamentoRepository.create({
          ...data,
          status: 'pendente' // Status inicial sempre pendente
        });

        console.log(`[PaymentService] Pagamento criado com ID ${pagamento.id}`);

        // 2. Criar evento na outbox (mesma transação)
        const outboxEvent = this.outboxService.createPaymentEvent(pagamento);

        console.log(`[PaymentService] Evento outbox criado com ID ${outboxEvent.id} e external_id ${outboxEvent.external_id}`);

        return {
          pagamento,
          outboxEvent
        };

      } catch (error) {
        console.error('[PaymentService] Erro na transação atômica:', error);
        throw error; // Vai causar rollback da transação
      }
    });
  }

  public getPaymentById(id: number): Pagamento | undefined {
    return this.pagamentoRepository.findById(id);
  }

  public getAllPayments(): Pagamento[] {
    return this.pagamentoRepository.findAll();
  }

  public updatePaymentStatus(id: number, status: Pagamento['status']): Pagamento | undefined {
    console.log(`[PaymentService] Atualizando status do pagamento ${id} para ${status}`);
    return this.pagamentoRepository.updateStatus(id, status);
  }

  public deletePayment(id: number): boolean {
    return this.pagamentoRepository.deleteById(id);
  }

  // Métodos para compatibilidade com código existente
  public updatePayment(id: number, updates: any): Pagamento | undefined {
    return this.pagamentoRepository.update(id, updates);
  }
}
