import { OutboxService } from './outbox-service';
import { PaymentExternalService } from './payment-external-service';
import { PagamentoRepository } from '../database/repositories/pagamento-repository';
import { OutboxEvent, OutboxEventPayload } from '../database/types/outbox';

export class BackgroundWorker {
  private outboxService: OutboxService;
  private paymentExternalService: PaymentExternalService;
  private pagamentoRepository: PagamentoRepository;
  private isRunning = false;
  private intervalId?: NodeJS.Timeout;
  private readonly processInterval = 1000; // 1 segundo para velocidade máxima

  constructor() {
    this.outboxService = new OutboxService();
    this.paymentExternalService = new PaymentExternalService();
    this.pagamentoRepository = new PagamentoRepository();
  }

  public start(): void {
    if (this.isRunning) {
      console.log('[BackgroundWorker] Worker j<PERSON> está rodando');
      return;
    }

    this.isRunning = true;
    console.log('[BackgroundWorker] Iniciando background worker para processamento de eventos outbox');

    // Processar imediatamente
    this.processEvents();

    // Configurar processamento contínuo
    this.intervalId = setInterval(() => {
      console.log('[BackgroundWorker] Processando eventos...');
      this.processEvents();
    }, this.processInterval);
  }

  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
    console.log('[BackgroundWorker] Background worker parado');
  }

  private async processEvents(): Promise<void> {
    try {
      const pendingEvents = this.outboxService.getPendingEvents(5); // Processar até 5 eventos por vez
      
      if (pendingEvents.length === 0) {
        console.log('[BackgroundWorker] Nenhum evento pendente para processar');
      }

      console.log(`[BackgroundWorker] Processando ${pendingEvents.length} eventos pendentes`);

      for (const event of pendingEvents) {
        await this.processEvent(event);
      }

      // Processar eventos que falharam e estão prontos para retry
      await this.processFailedEvents();

    } catch (error) {
      console.error('[BackgroundWorker] Erro no processamento de eventos:', error);
    }
  }

  private async processEvent(event: OutboxEvent): Promise<void> {
    try {
      console.log(`[BackgroundWorker] Processando evento ${event.id} (${event.event_type})`);

      const payload: OutboxEventPayload = JSON.parse(event.payload);
      const result = await this.paymentExternalService.sendPayment(payload);

      if (result.success) {
        // Sucesso - marcar como processado e atualizar status do pagamento
        this.outboxService.markAsProcessed(event.id);
        this.pagamentoRepository.update(event.aggregate_id, { status: 'enviado' });

        console.log(`[BackgroundWorker] Evento ${event.id} processado com sucesso`);
      } else {
        // Falha - incrementar retry count e salvar mensagem de erro
        const newRetryCount = event.retry_count + 1;
        const errorMessage = `Status ${result.statusCode}: ${result.error}`;
        this.outboxService.markAsFailed(event.id, newRetryCount, errorMessage);

        console.log(`[BackgroundWorker] Evento ${event.id} falhou (tentativa ${newRetryCount}/3):`, result.error);
      }

    } catch (error) {
      console.error(`[BackgroundWorker] Erro ao processar evento ${event.id}:`, error);

      // Marcar como falha em caso de erro inesperado
      const newRetryCount = event.retry_count + 1;
      const errorMessage = `Erro inesperado: ${error instanceof Error ? error.message : String(error)}`;
      this.outboxService.markAsFailed(event.id, newRetryCount, errorMessage);
    }
  }

  private async processFailedEvents(): Promise<void> {
    const failedEvents = this.outboxService.getFailedEvents();
    console.log(`[BackgroundWorker] Processando ${failedEvents.length} eventos falhos`);

    for (const event of failedEvents) {
      // Para eventos antigos sem last_retry_at, definir como agora - delay para permitir retry imediato
      if (!event.last_retry_at && event.retry_count > 0) {
        const delayMs = this.getRetryDelay(event.retry_count - 1);
        const adjustedTime = new Date(Date.now() - delayMs - 1000); // 1 segundo a mais para garantir

        console.log(`[BackgroundWorker] Ajustando evento antigo ${event.id} sem last_retry_at`);

        // Atualizar o evento com last_retry_at ajustado
        const outboxRepository = new (require('../database/repositories/outbox-repository').OutboxRepository)();
        outboxRepository.update(event.id, {
          last_retry_at: adjustedTime.toISOString()
        });

        // Recarregar o evento atualizado
        const updatedEvent = outboxRepository.findById(event.id);
        if (updatedEvent && this.shouldRetry(updatedEvent)) {
          this.outboxService.resetFailedEvent(event.id);
          console.log(`[BackgroundWorker] Evento ${event.id} resetado para retry (tentativa ${event.retry_count + 1}/3)`);
        }
      } else if (this.shouldRetry(event)) {
        // Reset para pending para tentar novamente
        this.outboxService.resetFailedEvent(event.id);
        console.log(`[BackgroundWorker] Evento ${event.id} resetado para retry (tentativa ${event.retry_count + 1}/3)`);
      }
    }
  }

  private shouldRetry(event: OutboxEvent): boolean {
    if (event.retry_count >= 3) {
      console.log(`[BackgroundWorker] Evento ${event.id} atingiu máximo de tentativas (${event.retry_count}/3)`);
      return false; // Máximo de tentativas atingido
    }

    // Calcular delay baseado no retry count (backoff exponencial)
    const delayMs = this.getRetryDelay(event.retry_count);
    const now = Date.now();

    // Usar last_retry_at se disponível, senão usar created_at
    const lastAttemptTime = event.last_retry_at
      ? new Date(event.last_retry_at).getTime()
      : new Date(event.created_at).getTime();

    const timeSinceLastAttempt = now - lastAttemptTime;

    console.log(`[BackgroundWorker] Evento ${event.id}: retry_count=${event.retry_count}, timeSinceLastAttempt=${timeSinceLastAttempt}ms, requiredDelay=${delayMs}ms`);

    const shouldRetry = timeSinceLastAttempt >= delayMs;

    if (!shouldRetry) {
      console.log(`[BackgroundWorker] Evento ${event.id} ainda em delay (faltam ${delayMs - timeSinceLastAttempt}ms)`);
    }

    return shouldRetry;
  }

  private getRetryDelay(retryCount: number): number {
    // Backoff exponencial: 2s, 4s, 8s
    const baseDelay = 2000; // 2 segundos
    return baseDelay * Math.pow(2, retryCount);
  }

  public getStatus() {
    return {
      isRunning: this.isRunning,
      statistics: this.outboxService.getStatistics()
    };
  }
}
