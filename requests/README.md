# API Tests - Transactional Outbox Pattern

Esta pasta contém testes HTTP organizados para validar a API de pagamentos com o padrão Transactional Outbox implementado.

## 📁 Estrutura dos Arquivos

```
requests/
├── config.http                 # Configurações globais (URLs, headers)
├── health.http                 # Testes de health check e documentação
├── pagamentos.http             # Testes CRUD de pagamentos
├── outbox-monitoring.http      # Monitoramento da outbox
├── outbox-admin.http           # Administração da outbox
├── validation-guide.http       # Guia completo de validação
├── api-tests.http             # Arquivo original (deprecated)
└── README.md                  # Esta documentação
```

## 🚀 Como Usar

### **Pré-requisitos:**
- Aplica<PERSON> rodando em `http://localhost:3000`
- VS Code com extensão "REST Client" OU IntelliJ/WebStorm

### **Ordem Recomendada de Execução:**

1. **`config.http`** - Verificar configurações
2. **`health.http`** - Verificar saúde da aplicação
3. **`validation-guide.http`** - Seguir guia completo de validação
4. **`pagamentos.http`** - Testes específicos de pagamentos
5. **`outbox-monitoring.http`** - Monitorar processamento
6. **`outbox-admin.http`** - Administrar eventos problemáticos

## 📋 Descrição dos Arquivos

### **`config.http`**
- Variáveis globais compartilhadas
- URLs base para diferentes ambientes
- Headers padrão

### **`health.http`**
- Health check da aplicação
- Verificação da documentação Swagger
- Testes básicos de conectividade

### **`pagamentos.http`**
- Criação de pagamentos (POST)
- Consulta de pagamentos (GET)
- Testes de validação
- Cenários de erro

### **`outbox-monitoring.http`**
- Estatísticas da outbox
- Eventos failed/error
- Busca de eventos específicos
- Monitoramento do processamento

### **`outbox-admin.http`**
- Reset de eventos failed
- Reset de eventos com erro
- Operações administrativas
- ⚠️ **Cuidado**: Afeta estado dos eventos

### **`validation-guide.http`**
- Guia passo-a-passo completo
- Fluxo de validação do Transactional Outbox
- Comportamento esperado
- Troubleshooting

## 🎯 Cenários de Teste Cobertos

### **✅ Fluxo Normal:**
- Pagamento criado → Status "pendente"
- Background worker processa → Envia para API externa
- API retorna 201 → Status atualizado para "enviado"

### **✅ Monitoramento:**
- Estatísticas em tempo real
- Eventos processados vs falhas
- Debugging de problemas

### **✅ Administração:**
- Reset de eventos problemáticos
- Reprocessamento de falhas
- Gestão de eventos com erro

### **✅ Validação:**
- Testes de entrada inválida
- Verificação de comportamento esperado
- Troubleshooting de problemas

## 🔧 Configuração de Ambiente

As variáveis são gerenciadas centralmente no arquivo `config.http` e sincronizadas automaticamente.

### **📁 Arquivos de Configuração:**
- ✅ **`config.http`** - 🎯 **FONTE ÚNICA DA VERDADE** - Defina variáveis aqui
- ✅ **`sync-config.js`** - Script para sincronizar variáveis automaticamente
- ✅ **`.vscode/settings.json`** - Configuração adicional para VS Code
- ✅ **`http-client.env.json`** - Arquivo de environment alternativo

### **🌍 Ambientes Disponíveis:**
- **`local`** (padrão): `http://localhost:3000`
- **`development`**: `https://api-dev.exemplo.com`
- **`production`**: `https://api.exemplo.com`

### **🔄 Como Trocar de Ambiente:**

#### **Método 1: Automático (Recomendado)**
```bash
# Opção A: Usando npm script (da raiz do projeto)
npm run sync-requests

# Opção B: Diretamente (da pasta requests)
cd requests
node sync-config.js
```

#### **Método 2: Manual**
1. Edite as variáveis em `config.http`
2. Copie as mesmas variáveis para todos os arquivos `.http`
3. Mantenha a consistência manualmente

### **📋 Exemplo de Mudança de Ambiente:**
```bash
# 1. Editar config.http (descomente a linha do ambiente desejado)
# @baseUrl = https://api-dev.exemplo.com  # Desenvolvimento

# 2. Sincronizar automaticamente
npm run sync-requests

# 3. Todas as variáveis são atualizadas em todos os arquivos!
```

### **⚡ Sincronização Automática:**
O script `sync-config.js` garante que todas as variáveis sejam consistentes:
- 📝 Lê variáveis do `config.http`
- 🔄 Atualiza todos os arquivos `.http` automaticamente
- ✅ Mantém consistência entre todos os testes

## 📊 Validação do Transactional Outbox

### **Comportamento Esperado:**

1. **Logs do Servidor:**
```
[PaymentService] Pagamento criado com ID X
[PaymentService] Evento outbox criado com ID Y
[BackgroundWorker] Processando evento Y
[PaymentExternalService] Enviando pagamento para API externa
[BackgroundWorker] Evento Y processado com sucesso
```

2. **Status dos Pagamentos:**
- Inicial: `"pendente"`
- Final: `"enviado"`

3. **Estatísticas da Outbox:**
```json
{
  "pending": 0,
  "processed": 5,
  "failed": 0,
  "error": 0
}
```

## 🛠️ Troubleshooting

### **Problema: Pagamentos ficam "pendente"**
- Verificar se background worker está rodando
- Verificar logs de erro
- Usar `outbox-monitoring.http` para investigar

### **Problema: Muitos eventos "failed"**
- API externa pode estar indisponível
- Usar `outbox-admin.http` para resetar eventos
- Verificar URL da API externa

### **Problema: Background worker não processa**
- Verificar logs do servidor
- Reiniciar aplicação
- Verificar configuração do worker

## 📝 Notas Importantes

- **Arquivo `api-tests.http`**: Mantido para compatibilidade, mas use os arquivos separados
- **Operações administrativas**: Use com cuidado, afetam o estado dos eventos
- **Monitoramento**: Execute periodicamente para acompanhar o sistema
- **Logs**: Sempre observe os logs do servidor durante os testes

## 🎉 Resultado Esperado

Após executar os testes, você deve observar:
- ✅ Pagamentos sendo criados e processados automaticamente
- ✅ Status mudando de "pendente" para "enviado"
- ✅ Eventos outbox sendo processados pelo background worker
- ✅ Retry automático em caso de falhas temporárias
- ✅ Logs detalhados para monitoramento e debugging
