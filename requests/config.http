# ⚙️  Configuração de Referência para Testes da API
#
# 📋 IMPORTANTE: Este arquivo é apenas para REFERÊNCIA
# 🎯 As variáveis reais são configuradas em .vscode/settings.json
#
# 🔄 Para mudar ambiente:
# 1. Use o seletor de ambiente do VS Code (canto inferior direito)
# 2. Ou edite .vscode/settings.json diretamente

### 🌍 Ambientes disponíveis (configurados em .vscode/settings.json)

# Local (padrão)
# baseUrl: http://localhost:3000

# Desenvolvimento
# baseUrl: https://api-dev.exemplo.com

# Produção
# baseUrl: https://api.exemplo.com

### 📝 COMO USAR:
# - No VS Code: Use o seletor de ambiente na barra de status
# - As variáveis {{baseUrl}} e {{contentType}} são reconhecidas automaticamente
# - Não precisa definir variáveis em cada arquivo .http
