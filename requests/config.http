# Configuração Global para Testes da API
# Este arquivo contém variáveis compartilhadas entre todos os testes
#
# Como usar:
# - VS Code: Instale a extensão "REST Client" 
# - IntelliJ: Use o HTTP Client integrado
# - Ou copie as requisições para seu cliente HTTP favorito (Postman, Insomnia, etc.)

### Configuração de variáveis globais
@baseUrl = http://localhost:3000
@contentType = application/json

### Variáveis para uso em templates
# Para usar com {{baseUrl}} nos arquivos .http
@baseUrl = http://localhost:3000

### Variáveis para diferentes ambientes
# @baseUrl = http://localhost:3000  # Local
# @baseUrl = https://api-dev.exemplo.com  # Desenvolvimento
# @baseUrl = https://api.exemplo.com  # Produção
