# API Tests - Transactional Outbox Pattern
# Este arquivo contém requisições HTTP para testar a API de pagamentos
# com o padrão Transactional Outbox implementado
#
# Como usar:
# - VS Code: Instale a extensão "REST Client" e clique em "Send Request"
# - IntelliJ: Use o HTTP Client integrado
# - Ou copie as requisições para seu cliente HTTP favorito (Postman, Insomnia, etc.)

### Configuração de variáveis
@baseUrl = http://localhost:3000
@contentType = application/json

###############################################################################
# HEALTH CHECK
###############################################################################

### Health Check - Verificar se a aplicação está funcionando
GET {{baseUrl}}/health

###############################################################################
# CRIAÇÃO DE PAGAMENTOS (POST)
# Estas requisições testam a transação atômica: pagamento + evento outbox
###############################################################################

### 1. Criar Pagamento - Compra Online
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 100.50,
  "descricao": "Compra online - Produto eletrônico"
}

###

### 2. Criar Pagamento - Assinatura Mensal
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 250.75,
  "descricao": "Assinatura mensal - Plano Premium"
}

###

### 3. Criar Pagamento - Recarga de Créditos
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 50.00,
  "descricao": "Recarga de créditos - Carteira digital"
}

###

### 4. Criar Pagamento - Compra de Alto Valor
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 999.99,
  "descricao": "Compra de alto valor - Notebook gamer"
}

###

### 5. Criar Pagamento - Micro Transação
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 25.30,
  "descricao": "Micro transação - Upgrade de funcionalidade"
}

###############################################################################
# VALIDAÇÃO DOS PAGAMENTOS CRIADOS (GET)
# Estas requisições verificam se os pagamentos foram criados corretamente
# e se o status foi atualizado de "pendente" para "enviado"
###############################################################################

### Listar todos os pagamentos
# Deve mostrar todos os pagamentos criados com status "enviado" (se processados)
GET {{baseUrl}}/pagamentos

###

### Buscar pagamento específico por ID - Pagamento 1
GET {{baseUrl}}/pagamentos/3

###

### Buscar pagamento específico por ID - Pagamento 2
GET {{baseUrl}}/pagamentos/2

###

### Buscar pagamento específico por ID - Pagamento 3
GET {{baseUrl}}/pagamentos/3

###

### Buscar pagamento específico por ID - Pagamento 4
GET {{baseUrl}}/pagamentos/4

###

### Buscar pagamento específico por ID - Pagamento 5
GET {{baseUrl}}/pagamentos/5

###############################################################################
# MONITORAMENTO DO TRANSACTIONAL OUTBOX
# Estas requisições verificam o funcionamento do padrão Transactional Outbox
###############################################################################

### Estatísticas da Outbox
# Mostra quantos eventos estão: pending, processed, failed, error
# Após processar os pagamentos, deve mostrar eventos "processed"
GET {{baseUrl}}/outbox/statistics

###

### Eventos que falharam (para debugging)
# Lista eventos que falharam no envio para API externa
GET {{baseUrl}}/outbox/failed

###

### Eventos com erro (máximo de tentativas atingido)
# Lista eventos que esgotaram as 3 tentativas de retry
GET {{baseUrl}}/outbox/errors

###############################################################################
# INSTRUÇÕES PARA VALIDAÇÃO DO TRANSACTIONAL OUTBOX
###############################################################################

# Para validar se o padrão Transactional Outbox está funcionando:
#
# 1. Execute as 5 requisições POST de criação de pagamentos
# 2. Observe os logs do servidor - deve mostrar:
#    - [PaymentService] Pagamento criado com ID X
#    - [PaymentService] Evento outbox criado com ID Y
#    - [BackgroundWorker] Processando evento Y
#    - [PaymentExternalService] Enviando pagamento para API externa
#    - [BackgroundWorker] Evento Y processado com sucesso
#
# 3. Execute GET /pagamentos - todos devem ter status "enviado"
# 4. Execute GET /outbox/statistics - deve mostrar eventos "processed"
# 5. Se houver falhas, verifique GET /outbox/failed e GET /outbox/errors
#
# Comportamento esperado:
# - Status inicial: "pendente" (ao criar)
# - Status final: "enviado" (após processamento bem-sucedido)
# - Background worker processa eventos automaticamente
# - Retry automático em caso de falha (máx 3 tentativas)
# - Logs detalhados para monitoramento
