# 🚀 Quick Start - API Tests

## ⚡ <PERSON><PERSON><PERSON> (2 minutos)

### **1. Verificar se a aplicação está rodando:**
```bash
# Terminal 1: Iniciar aplicação
npm start

# Terminal 2: Testar health check
curl http://localhost:3000/health
```

### **2. Executar testes básicos:**
- Abra `validation-guide.http` no VS Code
- Execute as requisições na ordem (Ctrl+Alt+R ou clique em "Send Request")
- Observe os logs do servidor

### **3. Mudar ambiente (se necessário):**
```bash
# Editar config.http e executar:
npm run sync-requests
```

---

## 📁 Arquivos Principais

| Arquivo | Propósito | Quando Usar |
|---------|-----------|-------------|
| `validation-guide.http` | 🎯 **Validação completa** | Primeiro teste, validação geral |
| `pagamentos.http` | 💳 Testes de pagamentos | Testar CRUD de pagamentos |
| `outbox-monitoring.http` | 📊 Monitoramento | Verificar status da outbox |
| `outbox-admin.http` | ⚙️ Administração | Resetar eventos problemáticos |
| `health.http` | 🏥 Health check | Verificar saúde da aplicação |

---

## 🎯 Fluxo de Validação Rápida

### **Cenário 1: Tudo funcionando**
1. `health.http` → Health check ✅
2. `validation-guide.http` → Criar 3 pagamentos ✅
3. Aguardar 5-10 segundos ⏱️
4. `outbox-monitoring.http` → Verificar estatísticas ✅
5. **Resultado esperado**: `"processed": 3, "pending": 0`

### **Cenário 2: Eventos presos**
1. `outbox-monitoring.http` → Ver eventos failed/error ❌
2. `outbox-admin.http` → Resetar eventos ⚙️
3. Aguardar processamento ⏱️
4. `outbox-monitoring.http` → Verificar novamente ✅

---

## 🔧 Configuração de Ambiente

### **Arquivo Central:** `config.http`
```http
@baseUrl = http://localhost:3000        # Local
# @baseUrl = https://api-dev.exemplo.com  # Dev
# @baseUrl = https://api.exemplo.com      # Prod
```

### **Sincronizar Mudanças:**
```bash
npm run sync-requests
```

---

## 📊 Interpretando Resultados

### **Estatísticas da Outbox:**
```json
{
  "pending": 0,     // ✅ Deve ser 0 (todos processados)
  "processed": 5,   // ✅ Eventos processados com sucesso
  "failed": 0,      // ⚠️ Falhas temporárias (retry automático)
  "error": 0        // ❌ Falhas permanentes (máx tentativas)
}
```

### **Status dos Pagamentos:**
- `"pendente"` → Criado, aguardando processamento
- `"enviado"` → ✅ Processado com sucesso

---

## 🆘 Troubleshooting Rápido

| Problema | Solução |
|----------|---------|
| `baseUrl is not found` | Execute `npm run sync-requests` |
| Pagamentos ficam "pendente" | Verificar logs do servidor, usar `outbox-admin.http` |
| Muitos eventos "failed" | API externa indisponível, aguardar ou resetar |
| Aplicação não responde | Verificar se está rodando na porta 3000 |

---

## 🎉 Sucesso!

Se você vê:
- ✅ Health check respondendo
- ✅ Pagamentos mudando de "pendente" para "enviado"
- ✅ Estatísticas mostrando eventos "processed"
- ✅ Logs do background worker processando eventos

**🎯 O padrão Transactional Outbox está funcionando perfeitamente!**
