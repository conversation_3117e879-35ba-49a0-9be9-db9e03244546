# Outbox Administration Tests
# Testes para administração e gerenciamento da Outbox
#
# 🌍 Ambiente: Configurado automaticamente via .vscode/settings.json

###############################################################################
# ADMINISTRAÇÃO DA OUTBOX
# Rotas administrativas para gerenciar eventos problemáticos
# ⚠️  CUIDADO: Estas operações afetam o estado dos eventos na outbox
###############################################################################

### Resetar eventos failed para pending
# Reseta eventos que falharam (mas não atingiram máximo de tentativas) para pending
# Útil quando a API externa estava temporariamente indisponível
POST {{baseUrl}}/outbox/admin/reset-failed

###

### Resetar eventos com erro para pending
# Reseta eventos que atingiram máximo de tentativas (status=error) para pending
# ⚠️  CUIDADO: Isso zera o retry_count para 0
POST {{baseUrl}}/outbox/admin/reset-all-errors

###############################################################################
# FLUXO DE TESTE COMPLETO PARA ADMINISTRAÇÃO
###############################################################################

### 1. Verificar estatísticas antes da operação
GET {{baseUrl}}/outbox/statistics

###

### 2. Verificar eventos com erro
GET {{baseUrl}}/outbox/errors

###

### 3. Resetar eventos com erro (se houver)
POST {{baseUrl}}/outbox/admin/reset-all-errors

###

### 4. Verificar estatísticas após reset
GET {{baseUrl}}/outbox/statistics

###

### 5. Aguardar processamento e verificar novamente
# Aguarde alguns segundos e execute novamente
GET {{baseUrl}}/outbox/statistics

###############################################################################
# CENÁRIOS DE TESTE ESPECÍFICOS
###############################################################################

### Cenário 1: Reset de eventos failed
# 1. Verificar se há eventos failed
GET {{baseUrl}}/outbox/failed

# 2. Resetar eventos failed
POST {{baseUrl}}/outbox/admin/reset-failed

# 3. Verificar se foram resetados
GET {{baseUrl}}/outbox/statistics

###

### Cenário 2: Reset de eventos com erro máximo
# 1. Verificar eventos com erro
GET {{baseUrl}}/outbox/errors

# 2. Resetar todos os eventos com erro
POST {{baseUrl}}/outbox/admin/reset-all-errors

# 3. Verificar resultado
GET {{baseUrl}}/outbox/statistics

###############################################################################
# INSTRUÇÕES DE USO
###############################################################################

# QUANDO USAR reset-failed:
# - API externa estava temporariamente indisponível
# - Problemas de rede temporários
# - Eventos com retry_count < 3

# QUANDO USAR reset-all-errors:
# - Após correção de bug na API externa
# - Mudança de endpoint da API externa
# - Eventos que esgotaram tentativas mas devem ser reprocessados
# - ⚠️  Use com cuidado: zera retry_count para 0
