# Validation Guide - Transactional Outbox Pattern
# Guia completo para validar se o padrão Transactional Outbox está funcionando
#
# 🌍 Ambiente: Configurado automaticamente via .vscode/settings.json

###############################################################################
# GUIA DE VALIDAÇÃO DO TRANSACTIONAL OUTBOX
###############################################################################

# Este arquivo contém um fluxo completo de testes para validar
# se o padrão Transactional Outbox está funcionando corretamente

###############################################################################
# PASSO 1: VERIFICAR SAÚDE DA APLICAÇÃO
###############################################################################

### 1.1 Health Check
GET {{baseUrl}}/health

### 1.2 Verificar estatísticas iniciais
GET {{baseUrl}}/outbox/statistics

###############################################################################
# PASSO 2: CRIAR PAGAMENTOS DE TESTE
###############################################################################

### 2.1 Criar Pagamento Teste 1
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 100.00,
  "descricao": "Teste Transactional Outbox - Pagamento 1"
}

### 2.2 Criar Pagamento Teste 2
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 250.50,
  "descricao": "Teste Transactional Outbox - Pagamento 2"
}

### 2.3 Criar Pagamento Teste 3
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 50.75,
  "descricao": "Teste Transactional Outbox - Pagamento 3"
}

###############################################################################
# PASSO 3: VERIFICAR CRIAÇÃO DOS PAGAMENTOS
###############################################################################

### 3.1 Listar todos os pagamentos
GET {{baseUrl}}/pagamentos

### 3.2 Verificar estatísticas da outbox após criação
GET {{baseUrl}}/outbox/statistics

###############################################################################
# PASSO 4: AGUARDAR PROCESSAMENTO (5-10 segundos)
###############################################################################

# Aguarde alguns segundos para o background worker processar os eventos
# Observe os logs do servidor para ver o processamento

### 4.1 Verificar estatísticas após processamento
GET {{baseUrl}}/outbox/statistics

### 4.2 Verificar status dos pagamentos
GET {{baseUrl}}/pagamentos

###############################################################################
# PASSO 5: VALIDAR RESULTADOS ESPERADOS
###############################################################################

### 5.1 Verificar se pagamentos têm status "enviado"
# Os pagamentos devem ter mudado de "pendente" para "enviado"
GET {{baseUrl}}/pagamentos

### 5.2 Verificar eventos processados
# Deve mostrar eventos "processed" > 0
GET {{baseUrl}}/outbox/statistics

### 5.3 Verificar se não há eventos pendentes
# "pending" deve ser 0 se tudo foi processado
GET {{baseUrl}}/outbox/statistics

###############################################################################
# PASSO 6: TESTAR CENÁRIOS DE FALHA (OPCIONAL)
###############################################################################

### 6.1 Verificar eventos que falharam
GET {{baseUrl}}/outbox/failed

### 6.2 Verificar eventos com erro
GET {{baseUrl}}/outbox/errors

### 6.3 Buscar detalhes de um evento específico
# Substitua pelo ID de um evento real
GET {{baseUrl}}/outbox/events/1

###############################################################################
# COMPORTAMENTO ESPERADO
###############################################################################

# ✅ SUCESSO - O que você deve observar:
# 
# 1. LOGS DO SERVIDOR:
#    - [PaymentService] Pagamento criado com ID X
#    - [PaymentService] Evento outbox criado com ID Y
#    - [BackgroundWorker] Processando evento Y
#    - [PaymentExternalService] Enviando pagamento para API externa
#    - [BackgroundWorker] Evento Y processado com sucesso
#
# 2. STATUS DOS PAGAMENTOS:
#    - Status inicial: "pendente" (ao criar)
#    - Status final: "enviado" (após processamento bem-sucedido)
#
# 3. ESTATÍSTICAS DA OUTBOX:
#    - "pending": 0 (todos processados)
#    - "processed": > 0 (eventos processados com sucesso)
#    - "failed": 0 ou poucos (falhas temporárias)
#    - "error": 0 ou poucos (falhas permanentes)
#
# 4. COMPORTAMENTO DO BACKGROUND WORKER:
#    - Processa eventos automaticamente
#    - Retry automático em caso de falha (máx 3 tentativas)
#    - Backoff exponencial: 2s → 4s → 8s
#    - Logs detalhados para monitoramento

###############################################################################
# TROUBLESHOOTING
###############################################################################

# ❌ PROBLEMAS COMUNS:
#
# 1. Pagamentos ficam com status "pendente":
#    - Verificar se background worker está rodando
#    - Verificar logs de erro na API externa
#    - Verificar GET /outbox/failed para eventos com falha
#
# 2. Muitos eventos "failed" ou "error":
#    - API externa pode estar indisponível
#    - Verificar URL da API externa
#    - Usar POST /outbox/admin/reset-failed para reprocessar
#
# 3. Background worker não processa eventos:
#    - Verificar se aplicação iniciou corretamente
#    - Verificar logs do servidor
#    - Reiniciar aplicação se necessário
