# Pagamentos API Tests
# Testes para operações CRUD de pagamentos

### Configuração de variáveis (importadas de config.http)
@baseUrl = http://localhost:3000
@contentType = application/json

###############################################################################
# CRIAÇÃO DE PAGAMENTOS (POST)
# Estas requisições testam a transação atômica: pagamento + evento outbox
###############################################################################

### 1. Criar Pagamento - Compra Online
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 100.50,
  "descricao": "Compra online - Produto eletrônico"
}

###

### 2. Criar Pagamento - Assinatura Mensal
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 250.75,
  "descricao": "Assinatura mensal - Plano Premium"
}

###

### 3. Criar Pagamento - Recarga de Créditos
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 50.00,
  "descricao": "Recarga de créditos - Carteira digital"
}

###

### 4. Criar Pagamento - Compra de Alto Valor
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 999.99,
  "descricao": "Compra de alto valor - Notebook gamer"
}

###

### 5. Criar Pagamento - Micro Transação
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 0.10,
  "descricao": "Micro transação - Upgrade de funcionalidade"
}

###############################################################################
# CONSULTA DE PAGAMENTOS (GET)
# Estas requisições verificam se os pagamentos foram criados corretamente
# e se o status foi atualizado de "pendente" para "enviado"
###############################################################################

### Listar todos os pagamentos
# Deve mostrar todos os pagamentos criados com status "enviado" (se processados)
GET {{baseUrl}}/pagamentos

###

### Buscar pagamento específico por ID - Pagamento 1
GET {{baseUrl}}/pagamentos/1

###

### Buscar pagamento específico por ID - Pagamento 2
GET {{baseUrl}}/pagamentos/2

###

### Buscar pagamento específico por ID - Pagamento 3
GET {{baseUrl}}/pagamentos/3

###

### Buscar pagamento específico por ID - Pagamento 4
GET {{baseUrl}}/pagamentos/4

###

### Buscar pagamento específico por ID - Pagamento 5
GET {{baseUrl}}/pagamentos/5

###############################################################################
# TESTES DE VALIDAÇÃO
###############################################################################

### Teste de validação - Valor inválido (negativo)
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": -10.00,
  "descricao": "Teste valor negativo"
}

###

### Teste de validação - Descrição vazia
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "valor": 100.00,
  "descricao": ""
}

###

### Teste de validação - Campos obrigatórios ausentes
POST {{baseUrl}}/pagamentos
Content-Type: {{contentType}}

{
  "descricao": "Teste sem valor"
}
