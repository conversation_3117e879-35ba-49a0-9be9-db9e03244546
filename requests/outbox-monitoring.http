# Outbox Monitoring Tests
# Testes para monitoramento do padrão Transactional Outbox

### Importar configurações
< config.http

###############################################################################
# MONITORAMENTO DO TRANSACTIONAL OUTBOX
# Estas requisições verificam o funcionamento do padrão Transactional Outbox
###############################################################################

### Estatísticas da Outbox
# Mostra quantos eventos estão: pending, processed, failed, error
# Após processar os pagamentos, deve mostrar eventos "processed"
GET {{baseUrl}}/outbox/statistics

###

### Eventos que falharam (para debugging)
# Lista eventos que falharam no envio para API externa
GET {{baseUrl}}/outbox/failed

###

### Eventos com erro (máximo de tentativas atingido)
# Lista eventos que esgotaram as 3 tentativas de retry
GET {{baseUrl}}/outbox/errors

###

### Buscar evento específico por ID
# Substitua "40" pelo ID do evento que deseja consultar
# Mostra detalhes completos incluindo error_message
GET {{baseUrl}}/outbox/events/40

###

### Buscar evento por ID - Exemplo com ID 1
GET {{baseUrl}}/outbox/events/1

###

### Buscar evento por ID - Exemplo com ID 10
GET {{baseUrl}}/outbox/events/10

###

### Buscar evento por ID - Exemplo com ID 20
GET {{baseUrl}}/outbox/events/20

###############################################################################
# TESTES DE CENÁRIOS ESPECÍFICOS
###############################################################################

### Verificar eventos pendentes (deve estar vazio se tudo processado)
GET {{baseUrl}}/outbox/statistics
# Verificar se "pending": 0

###

### Verificar eventos processados com sucesso
GET {{baseUrl}}/outbox/statistics
# Verificar se "processed" > 0

###

### Buscar eventos que falharam recentemente
GET {{baseUrl}}/outbox/failed
# Verificar se há eventos com retry_count < 3

###

### Buscar eventos que esgotaram tentativas
GET {{baseUrl}}/outbox/errors
# Verificar se há eventos com retry_count >= 3
